# Copyright (c) 2014 The WebRTC project authors. All Rights Reserved.
#
# Use of this source code is governed by a BSD-style license
# that can be found in the LICENSE file in the root of the source
# tree. An additional intellectual property rights grant can be found
# in the file PATENTS.  All contributing project authors may
# be found in the AUTHORS file in the root of the source tree.

import("../webrtc.gni")

rtc_library("video") {
  sources = [
    "buffered_frame_decryptor.cc",
    "buffered_frame_decryptor.h",
    "call_stats2.cc",
    "call_stats2.h",
    "encoder_rtcp_feedback.cc",
    "encoder_rtcp_feedback.h",
    "quality_limitation_reason_tracker.cc",
    "quality_limitation_reason_tracker.h",
    "quality_threshold.cc",
    "quality_threshold.h",
    "receive_statistics_proxy2.cc",
    "receive_statistics_proxy2.h",
    "report_block_stats.cc",
    "report_block_stats.h",
    "rtp_streams_synchronizer2.cc",
    "rtp_streams_synchronizer2.h",
    "rtp_video_stream_receiver2.cc",
    "rtp_video_stream_receiver2.h",
    "rtp_video_stream_receiver_frame_transformer_delegate.cc",
    "rtp_video_stream_receiver_frame_transformer_delegate.h",
    "send_delay_stats.cc",
    "send_delay_stats.h",
    "send_statistics_proxy.cc",
    "send_statistics_proxy.h",
    "stats_counter.cc",
    "stats_counter.h",
    "stream_synchronization.cc",
    "stream_synchronization.h",
    "transport_adapter.cc",
    "transport_adapter.h",
    "video_quality_observer2.cc",
    "video_quality_observer2.h",
    "video_receive_stream2.cc",
    "video_receive_stream2.h",
    "video_send_stream.cc",
    "video_send_stream.h",
    "video_send_stream_impl.cc",
    "video_send_stream_impl.h",
    "video_stream_decoder2.cc",
    "video_stream_decoder2.h",
  ]

  deps = [
    ":frame_dumping_decoder",
    ":video_stream_encoder_impl",
    "../api:array_view",
    "../api:fec_controller_api",
    "../api:frame_transformer_interface",
    "../api:libjingle_peerconnection_api",
    "../api:rtp_parameters",
    "../api:scoped_refptr",
    "../api:sequence_checker",
    "../api:transport_api",
    "../api/crypto:frame_decryptor_interface",
    "../api/crypto:options",
    "../api/rtc_event_log",
    "../api/task_queue",
    "../api/units:time_delta",
    "../api/units:timestamp",
    "../api/video:encoded_image",
    "../api/video:recordable_encoded_frame",
    "../api/video:video_bitrate_allocation",
    "../api/video:video_bitrate_allocator",
    "../api/video:video_codec_constants",
    "../api/video:video_frame",
    "../api/video:video_rtp_headers",
    "../api/video:video_stream_encoder",
    "../api/video_codecs:video_codecs_api",
    "../call:bitrate_allocator",
    "../call:call_interfaces",
    "../call:rtp_interfaces",
    "../call:rtp_receiver",  # For RtxReceiveStream.
    "../call:rtp_sender",
    "../call:video_stream_api",
    "../common_video",
    "../modules:module_api",
    "../modules:module_api_public",
    "../modules/pacing",
    "../modules/remote_bitrate_estimator",
    "../modules/rtp_rtcp",
    "../modules/rtp_rtcp:rtp_rtcp_format",
    "../modules/rtp_rtcp:rtp_video_header",
    "../modules/utility",
    "../modules/video_coding",
    "../modules/video_coding:codec_globals_headers",
    "../modules/video_coding:nack_module",
    "../modules/video_coding:video_codec_interface",
    "../modules/video_coding:video_coding_utility",
    "../modules/video_processing",
    "../rtc_base:checks",
    "../rtc_base:rate_limiter",
    "../rtc_base:rtc_base",
    "../rtc_base:rtc_base_approved",
    "../rtc_base:rtc_numerics",
    "../rtc_base:rtc_task_queue",
    "../rtc_base:stringutils",
    "../rtc_base:threading",
    "../rtc_base:weak_ptr",
    "../rtc_base/experiments:alr_experiment",
    "../rtc_base/experiments:field_trial_parser",
    "../rtc_base/experiments:keyframe_interval_settings_experiment",
    "../rtc_base/experiments:min_video_bitrate_experiment",
    "../rtc_base/experiments:quality_scaling_experiment",
    "../rtc_base/experiments:rate_control_settings",
    "../rtc_base/synchronization:mutex",
    "../rtc_base/system:no_unique_address",
    "../rtc_base/system:thread_registry",
    "../rtc_base/task_utils:pending_task_safety_flag",
    "../rtc_base/task_utils:repeating_task",
    "../rtc_base/task_utils:to_queued_task",
    "../rtc_base/time:timestamp_extrapolator",
    "../system_wrappers",
    "../system_wrappers:field_trial",
    "../system_wrappers:metrics",
    "./adaptation:video_adaptation",
  ]
  absl_deps = [
    "//third_party/abseil-cpp/absl/algorithm:container",
    "//third_party/abseil-cpp/absl/base:core_headers",
    "//third_party/abseil-cpp/absl/memory",
    "//third_party/abseil-cpp/absl/strings",
    "//third_party/abseil-cpp/absl/types:optional",
  ]

  if (!build_with_mozilla) {
    deps += [ "../media:rtc_media_base" ]
  }
}

rtc_source_set("video_legacy") {
  sources = [
    "call_stats.cc",
    "call_stats.h",
    "receive_statistics_proxy.cc",
    "receive_statistics_proxy.h",
    "rtp_streams_synchronizer.cc",
    "rtp_streams_synchronizer.h",
    "rtp_video_stream_receiver.cc",
    "rtp_video_stream_receiver.h",
    "video_quality_observer.cc",
    "video_quality_observer.h",
    "video_receive_stream.cc",
    "video_receive_stream.h",
    "video_stream_decoder.cc",
    "video_stream_decoder.h",
  ]
  deps = [
    ":frame_dumping_decoder",
    ":video",
    "../api:array_view",
    "../api:scoped_refptr",
    "../api:sequence_checker",
    "../api/crypto:frame_decryptor_interface",
    "../api/task_queue",
    "../api/units:timestamp",
    "../api/video:encoded_image",
    "../api/video:recordable_encoded_frame",
    "../api/video:video_frame",
    "../api/video:video_rtp_headers",
    "../api/video_codecs:video_codecs_api",
    "../call:call_interfaces",
    "../call:rtp_interfaces",
    "../call:rtp_receiver",  # For RtxReceiveStream.
    "../call:video_stream_api",
    "../common_video",
    "../modules:module_api",
    "../modules/pacing",
    "../modules/remote_bitrate_estimator",
    "../modules/rtp_rtcp",
    "../modules/rtp_rtcp:rtp_rtcp_format",
    "../modules/rtp_rtcp:rtp_rtcp_legacy",
    "../modules/rtp_rtcp:rtp_video_header",
    "../modules/utility",
    "../modules/video_coding",
    "../modules/video_coding:video_codec_interface",
    "../modules/video_coding:video_coding_utility",
    "../modules/video_coding/deprecated:nack_module",
    "../rtc_base:checks",
    "../rtc_base:rtc_base_approved",
    "../rtc_base:rtc_numerics",
    "../rtc_base:rtc_task_queue",
    "../rtc_base/experiments:field_trial_parser",
    "../rtc_base/experiments:keyframe_interval_settings_experiment",
    "../rtc_base/synchronization:mutex",
    "../rtc_base/system:no_unique_address",
    "../rtc_base/system:thread_registry",
    "../rtc_base/task_utils:to_queued_task",
    "../system_wrappers",
    "../system_wrappers:field_trial",
    "../system_wrappers:metrics",
  ]
  if (!build_with_mozilla) {
    deps += [ "../media:rtc_media_base" ]
  }
  absl_deps = [
    "//third_party/abseil-cpp/absl/algorithm:container",
    "//third_party/abseil-cpp/absl/types:optional",
  ]
}

rtc_library("video_stream_decoder_impl") {
  visibility = [ "*" ]

  sources = [
    "video_stream_decoder_impl.cc",
    "video_stream_decoder_impl.h",
  ]

  deps = [
    "../api:sequence_checker",
    "../api/task_queue",
    "../api/video:encoded_frame",
    "../api/video:video_frame",
    "../api/video:video_rtp_headers",
    "../api/video:video_stream_decoder",
    "../api/video_codecs:video_codecs_api",
    "../modules/video_coding",
    "../rtc_base:rtc_base_approved",
    "../rtc_base:rtc_task_queue",
    "../rtc_base/synchronization:mutex",
    "../system_wrappers",
  ]
  absl_deps = [ "//third_party/abseil-cpp/absl/types:optional" ]
}

rtc_library("frame_dumping_decoder") {
  visibility = [ "*" ]

  sources = [
    "frame_dumping_decoder.cc",
    "frame_dumping_decoder.h",
  ]

  deps = [
    "../api/video:encoded_frame",
    "../api/video:encoded_image",
    "../api/video_codecs:video_codecs_api",
    "../modules/video_coding",
    "../modules/video_coding:video_codec_interface",
    "../modules/video_coding:video_coding_utility",
    "../rtc_base:rtc_base_approved",
    "../rtc_base/system:file_wrapper",
  ]
}

rtc_library("video_stream_encoder_impl") {
  visibility = [ "*" ]

  # visibility = [ "../api/video:video_stream_encoder_create" ]
  sources = [
    "alignment_adjuster.cc",
    "alignment_adjuster.h",
    "encoder_bitrate_adjuster.cc",
    "encoder_bitrate_adjuster.h",
    "encoder_overshoot_detector.cc",
    "encoder_overshoot_detector.h",
    "frame_encode_metadata_writer.cc",
    "frame_encode_metadata_writer.h",
    "video_source_sink_controller.cc",
    "video_source_sink_controller.h",
    "video_stream_encoder.cc",
    "video_stream_encoder.h",
  ]

  deps = [
    "../api:rtp_parameters",
    "../api:sequence_checker",
    "../api/adaptation:resource_adaptation_api",
    "../api/task_queue:task_queue",
    "../api/units:data_rate",
    "../api/video:encoded_image",
    "../api/video:video_adaptation",
    "../api/video:video_bitrate_allocation",
    "../api/video:video_bitrate_allocator",
    "../api/video:video_bitrate_allocator_factory",
    "../api/video:video_codec_constants",
    "../api/video:video_frame",
    "../api/video:video_layers_allocation",
    "../api/video:video_rtp_headers",
    "../api/video:video_stream_encoder",
    "../api/video_codecs:video_codecs_api",
    "../call/adaptation:resource_adaptation",
    "../common_video",
    "../modules:module_api_public",
    "../modules/video_coding",
    "../modules/video_coding:video_codec_interface",
    "../modules/video_coding:video_coding_utility",
    "../modules/video_coding:webrtc_vp9_helpers",
    "../rtc_base:checks",
    "../rtc_base:criticalsection",
    "../rtc_base:logging",
    "../rtc_base:rtc_base_approved",
    "../rtc_base:rtc_event",
    "../rtc_base:rtc_numerics",
    "../rtc_base:rtc_task_queue",
    "../rtc_base:timeutils",
    "../rtc_base/experiments:alr_experiment",
    "../rtc_base/experiments:balanced_degradation_settings",
    "../rtc_base/experiments:encoder_info_settings",
    "../rtc_base/experiments:field_trial_parser",
    "../rtc_base/experiments:quality_rampup_experiment",
    "../rtc_base/experiments:quality_scaler_settings",
    "../rtc_base/experiments:quality_scaling_experiment",
    "../rtc_base/experiments:rate_control_settings",
    "../rtc_base/synchronization:mutex",
    "../rtc_base/system:no_unique_address",
    "../rtc_base/task_utils:pending_task_safety_flag",
    "../rtc_base/task_utils:repeating_task",
    "../system_wrappers",
    "../system_wrappers:field_trial",
    "adaptation:video_adaptation",
  ]
  absl_deps = [
    "//third_party/abseil-cpp/absl/algorithm:container",
    "//third_party/abseil-cpp/absl/base:core_headers",
    "//third_party/abseil-cpp/absl/types:optional",
  ]
}

if (rtc_include_tests) {
  rtc_library("video_mocks") {
    testonly = true
    sources = [ "test/mock_video_stream_encoder.h" ]
    deps = [
      "../api/video:video_stream_encoder",
      "../test:test_support",
    ]
  }
  if (!build_with_chromium) {
    rtc_library("video_quality_test") {
      testonly = true

      # Only targets in this file and api/ can depend on this.
      visibility = [
        ":*",
        "../api:create_video_quality_test_fixture_api",
      ]
      sources = [
        "video_analyzer.cc",
        "video_analyzer.h",
        "video_quality_test.cc",
        "video_quality_test.h",
      ]
      deps = [
        ":frame_dumping_decoder",
        "../api:create_frame_generator",
        "../api:fec_controller_api",
        "../api:frame_generator_api",
        "../api:libjingle_peerconnection_api",
        "../api:rtc_event_log_output_file",
        "../api:test_dependency_factory",
        "../api:video_quality_test_fixture_api",
        "../api/numerics",
        "../api/rtc_event_log:rtc_event_log_factory",
        "../api/task_queue",
        "../api/task_queue:default_task_queue_factory",
        "../api/video:builtin_video_bitrate_allocator_factory",
        "../api/video:video_bitrate_allocator_factory",
        "../api/video:video_frame",
        "../api/video:video_rtp_headers",
        "../api/video_codecs:video_codecs_api",
        "../call:fake_network",
        "../call:simulated_network",
        "../common_video",
        "../media:rtc_audio_video",
        "../media:rtc_encoder_simulcast_proxy",
        "../media:rtc_internal_video_codecs",
        "../media:rtc_media_base",
        "../modules/audio_device:audio_device_api",
        "../modules/audio_device:audio_device_module_from_input_and_output",
        "../modules/audio_device:windows_core_audio_utility",
        "../modules/audio_mixer:audio_mixer_impl",
        "../modules/rtp_rtcp",
        "../modules/rtp_rtcp:rtp_rtcp_format",
        "../modules/video_coding",
        "../modules/video_coding:video_coding_utility",
        "../modules/video_coding:webrtc_h264",
        "../modules/video_coding:webrtc_multiplex",
        "../modules/video_coding:webrtc_vp8",
        "../modules/video_coding:webrtc_vp9",
        "../rtc_base:rtc_base_approved",
        "../rtc_base:rtc_base_tests_utils",
        "../rtc_base:rtc_numerics",
        "../rtc_base:task_queue_for_test",
        "../rtc_base/synchronization:mutex",
        "../rtc_base/task_utils:repeating_task",
        "../system_wrappers",
        "../test:fake_video_codecs",
        "../test:fileutils",
        "../test:perf_test",
        "../test:platform_video_capturer",
        "../test:rtp_test_utils",
        "../test:test_common",
        "../test:test_renderer",
        "../test:test_support",
        "../test:test_support_test_artifacts",
        "../test:video_test_common",
        "../test:video_test_support",
      ]
      absl_deps = [
        "//third_party/abseil-cpp/absl/algorithm:container",
        "//third_party/abseil-cpp/absl/flags:flag",
        "//third_party/abseil-cpp/absl/flags:parse",
      ]

      if (is_mac || is_ios) {
        deps += [ "../test:video_test_mac" ]
      }
    }

    rtc_library("video_full_stack_tests") {
      testonly = true

      sources = [ "full_stack_tests.cc" ]
      deps = [
        ":video_quality_test",
        "../api:simulated_network_api",
        "../api:test_dependency_factory",
        "../api:video_quality_test_fixture_api",
        "../api/video_codecs:video_codecs_api",
        "../modules/pacing",
        "../modules/video_coding:webrtc_vp9",
        "../rtc_base/experiments:alr_experiment",
        "../system_wrappers:field_trial",
        "../test:field_trial",
        "../test:fileutils",
        "../test:test_common",
        "../test:test_support",
        "//testing/gtest",
      ]
      absl_deps = [
        "//third_party/abseil-cpp/absl/flags:flag",
        "//third_party/abseil-cpp/absl/flags:parse",
        "//third_party/abseil-cpp/absl/types:optional",
      ]
    }

    rtc_library("video_pc_full_stack_tests") {
      testonly = true

      sources = [ "pc_full_stack_tests.cc" ]
      deps = [
        "../api:create_network_emulation_manager",
        "../api:create_peer_connection_quality_test_frame_generator",
        "../api:create_peerconnection_quality_test_fixture",
        "../api:frame_generator_api",
        "../api:media_stream_interface",
        "../api:network_emulation_manager_api",
        "../api:peer_connection_quality_test_fixture_api",
        "../api:simulated_network_api",
        "../api:time_controller",
        "../api/video_codecs:video_codecs_api",
        "../call:simulated_network",
        "../modules/video_coding:webrtc_vp9",
        "../system_wrappers:field_trial",
        "../test:field_trial",
        "../test:fileutils",
        "../test:test_support",
        "../test/pc/e2e:network_quality_metrics_reporter",
      ]
    }

    rtc_library("video_loopback_lib") {
      testonly = true
      sources = [
        "video_loopback.cc",
        "video_loopback.h",
      ]
      deps = [
        ":video_quality_test",
        "../api:libjingle_peerconnection_api",
        "../api:simulated_network_api",
        "../api:video_quality_test_fixture_api",
        "../api/transport:bitrate_settings",
        "../api/video_codecs:video_codecs_api",
        "../rtc_base:checks",
        "../rtc_base:logging",
        "../system_wrappers:field_trial",
        "../test:field_trial",
        "../test:run_test",
        "../test:run_test_interface",
        "../test:test_common",
        "../test:test_renderer",
        "../test:test_support",
        "//testing/gtest",
      ]
      absl_deps = [
        "//third_party/abseil-cpp/absl/flags:flag",
        "//third_party/abseil-cpp/absl/flags:parse",
        "//third_party/abseil-cpp/absl/types:optional",
      ]
    }

    if (is_mac) {
      mac_app_bundle("video_loopback") {
        testonly = true
        sources = [ "video_loopback_main.mm" ]
        info_plist = "../test/mac/Info.plist"
        deps = [ ":video_loopback_lib" ]
      }
    } else {
      rtc_executable("video_loopback") {
        testonly = true
        sources = [ "video_loopback_main.cc" ]
        deps = [ ":video_loopback_lib" ]
      }
    }

    rtc_executable("screenshare_loopback") {
      testonly = true
      sources = [ "screenshare_loopback.cc" ]

      deps = [
        ":video_quality_test",
        "../api:libjingle_peerconnection_api",
        "../api:simulated_network_api",
        "../api:video_quality_test_fixture_api",
        "../api/transport:bitrate_settings",
        "../api/video_codecs:video_codecs_api",
        "../rtc_base:checks",
        "../rtc_base:logging",
        "../rtc_base:stringutils",
        "../system_wrappers:field_trial",
        "../test:field_trial",
        "../test:run_test",
        "../test:run_test_interface",
        "../test:test_common",
        "../test:test_renderer",
        "../test:test_support",
        "//third_party/abseil-cpp/absl/flags:flag",
        "//third_party/abseil-cpp/absl/flags:parse",
        "//third_party/abseil-cpp/absl/types:optional",
      ]
    }

    rtc_executable("sv_loopback") {
      testonly = true
      sources = [ "sv_loopback.cc" ]
      deps = [
        ":video_quality_test",
        "../api:libjingle_peerconnection_api",
        "../api:simulated_network_api",
        "../api:video_quality_test_fixture_api",
        "../api/transport:bitrate_settings",
        "../api/video_codecs:video_codecs_api",
        "../rtc_base:checks",
        "../rtc_base:logging",
        "../rtc_base:stringutils",
        "../system_wrappers:field_trial",
        "../test:field_trial",
        "../test:run_test",
        "../test:run_test_interface",
        "../test:test_common",
        "../test:test_renderer",
        "../test:test_support",
        "//testing/gtest",
        "//third_party/abseil-cpp/absl/flags:flag",
        "//third_party/abseil-cpp/absl/flags:parse",
        "//third_party/abseil-cpp/absl/types:optional",
      ]
    }
  }

  # TODO(pbos): Rename test suite.
  rtc_library("video_tests") {
    testonly = true

    defines = []
    sources = [
      "alignment_adjuster_unittest.cc",
      "buffered_frame_decryptor_unittest.cc",
      "call_stats2_unittest.cc",
      "call_stats_unittest.cc",
      "cpu_scaling_tests.cc",
      "encoder_bitrate_adjuster_unittest.cc",
      "encoder_overshoot_detector_unittest.cc",
      "encoder_rtcp_feedback_unittest.cc",
      "end_to_end_tests/bandwidth_tests.cc",
      "end_to_end_tests/call_operation_tests.cc",
      "end_to_end_tests/codec_tests.cc",
      "end_to_end_tests/config_tests.cc",
      "end_to_end_tests/extended_reports_tests.cc",
      "end_to_end_tests/fec_tests.cc",
      "end_to_end_tests/frame_encryption_tests.cc",
      "end_to_end_tests/histogram_tests.cc",
      "end_to_end_tests/multi_codec_receive_tests.cc",
      "end_to_end_tests/multi_stream_tester.cc",
      "end_to_end_tests/multi_stream_tester.h",
      "end_to_end_tests/multi_stream_tests.cc",
      "end_to_end_tests/network_state_tests.cc",
      "end_to_end_tests/resolution_bitrate_limits_tests.cc",
      "end_to_end_tests/retransmission_tests.cc",
      "end_to_end_tests/rtp_rtcp_tests.cc",
      "end_to_end_tests/ssrc_tests.cc",
      "end_to_end_tests/stats_tests.cc",
      "end_to_end_tests/transport_feedback_tests.cc",
      "frame_encode_metadata_writer_unittest.cc",
      "picture_id_tests.cc",
      "quality_limitation_reason_tracker_unittest.cc",
      "quality_scaling_tests.cc",
      "quality_threshold_unittest.cc",
      "receive_statistics_proxy2_unittest.cc",
      "receive_statistics_proxy_unittest.cc",
      "report_block_stats_unittest.cc",
      "rtp_video_stream_receiver2_unittest.cc",
      "rtp_video_stream_receiver_frame_transformer_delegate_unittest.cc",
      "rtp_video_stream_receiver_unittest.cc",
      "send_delay_stats_unittest.cc",
      "send_statistics_proxy_unittest.cc",
      "stats_counter_unittest.cc",
      "stream_synchronization_unittest.cc",
      "video_receive_stream2_unittest.cc",
      "video_receive_stream_unittest.cc",
      "video_send_stream_impl_unittest.cc",
      "video_send_stream_tests.cc",
      "video_source_sink_controller_unittest.cc",
      "video_stream_decoder_impl_unittest.cc",
      "video_stream_encoder_unittest.cc",
    ]
    deps = [
      ":video",
      ":video_legacy",
      ":video_mocks",
      ":video_stream_decoder_impl",
      ":video_stream_encoder_impl",
      "../api:create_frame_generator",
      "../api:fake_frame_decryptor",
      "../api:fake_frame_encryptor",
      "../api:frame_generator_api",
      "../api:libjingle_peerconnection_api",
      "../api:mock_fec_controller_override",
      "../api:mock_frame_decryptor",
      "../api:mock_video_codec_factory",
      "../api:mock_video_encoder",
      "../api:rtp_headers",
      "../api:rtp_parameters",
      "../api:scoped_refptr",
      "../api:sequence_checker",
      "../api:simulated_network_api",
      "../api:transport_api",
      "../api/adaptation:resource_adaptation_api",
      "../api/crypto:options",
      "../api/rtc_event_log",
      "../api/task_queue",
      "../api/task_queue:default_task_queue_factory",
      "../api/test/video:function_video_factory",
      "../api/units:data_rate",
      "../api/units:timestamp",
      "../api/video:builtin_video_bitrate_allocator_factory",
      "../api/video:encoded_image",
      "../api/video:video_adaptation",
      "../api/video:video_bitrate_allocation",
      "../api/video:video_frame",
      "../api/video:video_frame_type",
      "../api/video:video_rtp_headers",
      "../api/video_codecs:video_codecs_api",
      "../api/video_codecs:vp8_temporal_layers_factory",
      "../call:call_interfaces",
      "../call:fake_network",
      "../call:mock_bitrate_allocator",
      "../call:mock_rtp_interfaces",
      "../call:rtp_interfaces",
      "../call:rtp_receiver",
      "../call:rtp_sender",
      "../call:simulated_network",
      "../call:simulated_packet_receiver",
      "../call:video_stream_api",
      "../call/adaptation:resource_adaptation",
      "../call/adaptation:resource_adaptation_test_utilities",
      "../common_video",
      "../common_video/test:utilities",
      "../media:rtc_audio_video",
      "../media:rtc_internal_video_codecs",
      "../media:rtc_media",
      "../media:rtc_media_base",
      "../media:rtc_media_tests_utils",
      "../media:rtc_simulcast_encoder_adapter",
      "../modules:module_api_public",
      "../modules/pacing",
      "../modules/rtp_rtcp",
      "../modules/rtp_rtcp:mock_rtp_rtcp",
      "../modules/rtp_rtcp:rtp_rtcp_format",
      "../modules/utility",
      "../modules/video_coding",
      "../modules/video_coding:codec_globals_headers",
      "../modules/video_coding:encoded_frame",
      "../modules/video_coding:video_codec_interface",
      "../modules/video_coding:video_coding_utility",
      "../modules/video_coding:webrtc_h264",
      "../modules/video_coding:webrtc_multiplex",
      "../modules/video_coding:webrtc_vp8",
      "../modules/video_coding:webrtc_vp9",
      "../modules/video_coding:webrtc_vp9_helpers",
      "../modules/video_coding/codecs/av1:libaom_av1_encoder",
      "../rtc_base",
      "../rtc_base:checks",
      "../rtc_base:gunit_helpers",
      "../rtc_base:rate_limiter",
      "../rtc_base:rtc_base_approved",
      "../rtc_base:rtc_base_tests_utils",
      "../rtc_base:rtc_numerics",
      "../rtc_base:rtc_task_queue",
      "../rtc_base:task_queue_for_test",
      "../rtc_base:threading",
      "../rtc_base/experiments:alr_experiment",
      "../rtc_base/experiments:encoder_info_settings",
      "../rtc_base/synchronization:mutex",
      "../rtc_base/task_utils:to_queued_task",
      "../system_wrappers",
      "../system_wrappers:field_trial",
      "../system_wrappers:metrics",
      "../test:direct_transport",
      "../test:encoder_settings",
      "../test:fake_video_codecs",
      "../test:field_trial",
      "../test:fileutils",
      "../test:frame_utils",
      "../test:mock_frame_transformer",
      "../test:mock_transport",
      "../test:null_transport",
      "../test:perf_test",
      "../test:rtp_test_utils",
      "../test:test_common",
      "../test:test_support",
      "../test:video_test_common",
      "../test/time_controller",
      "adaptation:video_adaptation",
      "//testing/gtest",
    ]
    absl_deps = [
      "//third_party/abseil-cpp/absl/algorithm:container",
      "//third_party/abseil-cpp/absl/memory",
      "//third_party/abseil-cpp/absl/types:optional",
    ]
    if (!build_with_mozilla) {
      deps += [ "../media:rtc_media_base" ]
    }
  }
}
