# Copyright (c) 2019 The WebRTC project authors. All Rights Reserved.
#
# Use of this source code is governed by a BSD-style license
# that can be found in the LICENSE file in the root of the source
# tree. An additional intellectual property rights grant can be found
# in the file PATENTS.  All contributing project authors may
# be found in the AUTHORS file in the root of the source tree.

import("../../../webrtc.gni")

if (!build_with_chromium) {
  group("e2e") {
    testonly = true

    deps = [
      ":encoded_image_data_injector_api",
      ":example_video_quality_analyzer",
      ":quality_analyzing_video_decoder",
      ":quality_analyzing_video_encoder",
      ":single_process_encoded_image_data_injector",
      ":video_frame_tracking_id_injector",
    ]
    if (rtc_include_tests) {
      deps += [
        ":peerconnection_quality_test",
        ":test_peer",
        ":video_quality_analyzer_injection_helper",
      ]
    }
  }

  if (rtc_include_tests) {
    group("e2e_unittests") {
      testonly = true

      deps = [
        ":default_video_quality_analyzer_test",
        ":multi_head_queue_test",
        ":peer_connection_e2e_smoke_test",
        ":single_process_encoded_image_data_injector_unittest",
        ":video_frame_tracking_id_injector_unittest",
      ]
    }
  }

  rtc_library("peer_connection_quality_test_params") {
    visibility = [ "*" ]
    testonly = true
    sources = [ "peer_connection_quality_test_params.h" ]

    deps = [
      "../../../api:callfactory_api",
      "../../../api:fec_controller_api",
      "../../../api:libjingle_peerconnection_api",
      "../../../api:packet_socket_factory",
      "../../../api:peer_connection_quality_test_fixture_api",
      "../../../api/rtc_event_log",
      "../../../api/task_queue",
      "../../../api/transport:network_control",
      "../../../api/transport:webrtc_key_value_config",
      "../../../api/video_codecs:video_codecs_api",
      "../../../rtc_base",
      "../../../rtc_base:threading",
    ]
  }

  rtc_library("encoded_image_data_injector_api") {
    visibility = [ "*" ]
    testonly = true
    sources = [ "analyzer/video/encoded_image_data_injector.h" ]

    deps = [ "../../../api/video:encoded_image" ]
  }

  rtc_library("single_process_encoded_image_data_injector") {
    visibility = [ "*" ]
    testonly = true
    sources = [
      "analyzer/video/single_process_encoded_image_data_injector.cc",
      "analyzer/video/single_process_encoded_image_data_injector.h",
    ]

    deps = [
      ":encoded_image_data_injector_api",
      "../../../api/video:encoded_image",
      "../../../rtc_base:checks",
      "../../../rtc_base:criticalsection",
      "../../../rtc_base/synchronization:mutex",
    ]
    absl_deps = [ "//third_party/abseil-cpp/absl/memory" ]
  }

  rtc_library("video_frame_tracking_id_injector") {
    visibility = [ "*" ]
    testonly = true
    sources = [
      "analyzer/video/video_frame_tracking_id_injector.cc",
      "analyzer/video/video_frame_tracking_id_injector.h",
    ]

    deps = [
      ":encoded_image_data_injector_api",
      "../../../api/video:encoded_image",
      "../../../rtc_base:checks",
    ]
    absl_deps = [ "//third_party/abseil-cpp/absl/memory" ]
  }

  rtc_library("simulcast_dummy_buffer_helper") {
    visibility = [ "*" ]
    testonly = true
    sources = [
      "analyzer/video/simulcast_dummy_buffer_helper.cc",
      "analyzer/video/simulcast_dummy_buffer_helper.h",
    ]
    deps = [ "../../../api/video:video_frame" ]
  }

  rtc_library("quality_analyzing_video_decoder") {
    visibility = [ "*" ]
    testonly = true
    sources = [
      "analyzer/video/quality_analyzing_video_decoder.cc",
      "analyzer/video/quality_analyzing_video_decoder.h",
    ]
    deps = [
      ":encoded_image_data_injector_api",
      ":simulcast_dummy_buffer_helper",
      "../../../api:video_quality_analyzer_api",
      "../../../api/video:encoded_image",
      "../../../api/video:video_frame",
      "../../../api/video:video_rtp_headers",
      "../../../api/video_codecs:video_codecs_api",
      "../../../modules/video_coding:video_codec_interface",
      "../../../rtc_base:criticalsection",
      "../../../rtc_base:logging",
      "../../../rtc_base/synchronization:mutex",
    ]
    absl_deps = [
      "//third_party/abseil-cpp/absl/strings",
      "//third_party/abseil-cpp/absl/types:optional",
    ]
  }

  rtc_library("quality_analyzing_video_encoder") {
    visibility = [ "*" ]
    testonly = true
    sources = [
      "analyzer/video/quality_analyzing_video_encoder.cc",
      "analyzer/video/quality_analyzing_video_encoder.h",
    ]
    deps = [
      ":encoded_image_data_injector_api",
      "../../../api:video_quality_analyzer_api",
      "../../../api/video:encoded_image",
      "../../../api/video:video_frame",
      "../../../api/video:video_rtp_headers",
      "../../../api/video_codecs:video_codecs_api",
      "../../../modules/video_coding:video_codec_interface",
      "../../../rtc_base:criticalsection",
      "../../../rtc_base:logging",
      "../../../rtc_base/synchronization:mutex",
    ]
    absl_deps = [ "//third_party/abseil-cpp/absl/strings" ]
  }

  if (rtc_include_tests) {
    rtc_library("video_quality_analyzer_injection_helper") {
      visibility = [ "*" ]
      testonly = true
      sources = [
        "analyzer/video/video_quality_analyzer_injection_helper.cc",
        "analyzer/video/video_quality_analyzer_injection_helper.h",
      ]
      deps = [
        ":encoded_image_data_injector_api",
        ":quality_analyzing_video_decoder",
        ":quality_analyzing_video_encoder",
        ":simulcast_dummy_buffer_helper",
        "../..:test_renderer",
        "../../../api:array_view",
        "../../../api:peer_connection_quality_test_fixture_api",
        "../../../api:stats_observer_interface",
        "../../../api:video_quality_analyzer_api",
        "../../../api/video:video_frame",
        "../../../api/video:video_rtp_headers",
        "../../../api/video_codecs:video_codecs_api",
        "../../../rtc_base:criticalsection",
        "../../../rtc_base/synchronization:mutex",
        "../../../test:video_test_common",
        "../../../test:video_test_support",
      ]
      absl_deps = [
        "//third_party/abseil-cpp/absl/memory",
        "//third_party/abseil-cpp/absl/strings",
      ]
    }

    rtc_library("echo_emulation") {
      visibility = [ "*" ]
      testonly = true
      sources = [
        "echo/echo_emulation.cc",
        "echo/echo_emulation.h",
      ]
      deps = [
        "../../../api:peer_connection_quality_test_fixture_api",
        "../../../modules/audio_device:audio_device_impl",
        "../../../rtc_base:rtc_base_approved",
      ]
    }

    rtc_library("test_peer") {
      visibility = [ "*" ]
      testonly = true
      sources = [
        "test_peer.cc",
        "test_peer.h",
      ]
      deps = [
        ":peer_configurer",
        ":peer_connection_quality_test_params",
        "../../../api:frame_generator_api",
        "../../../api:peer_connection_quality_test_fixture_api",
        "../../../api:scoped_refptr",
        "../../../modules/audio_processing:api",
        "../../../pc:peerconnection_wrapper",
      ]
      absl_deps = [
        "//third_party/abseil-cpp/absl/memory",
        "//third_party/abseil-cpp/absl/types:variant",
      ]
    }

    rtc_library("test_peer_factory") {
      visibility = [ "*" ]
      testonly = true
      sources = [
        "test_peer_factory.cc",
        "test_peer_factory.h",
      ]
      deps = [
        ":echo_emulation",
        ":peer_configurer",
        ":peer_connection_quality_test_params",
        ":quality_analyzing_video_encoder",
        ":test_peer",
        ":video_quality_analyzer_injection_helper",
        "../..:copy_to_file_audio_capturer",
        "../../../api:create_time_controller",
        "../../../api:peer_connection_quality_test_fixture_api",
        "../../../api:time_controller",
        "../../../api/rtc_event_log:rtc_event_log_factory",
        "../../../api/task_queue:default_task_queue_factory",
        "../../../api/transport:field_trial_based_config",
        "../../../api/video_codecs:builtin_video_decoder_factory",
        "../../../api/video_codecs:builtin_video_encoder_factory",
        "../../../media:rtc_audio_video",
        "../../../media:rtc_media_engine_defaults",
        "../../../modules/audio_device:audio_device_impl",
        "../../../modules/audio_processing/aec_dump",
        "../../../p2p:rtc_p2p",
        "../../../rtc_base:rtc_task_queue",
      ]
      absl_deps = [
        "//third_party/abseil-cpp/absl/memory",
        "//third_party/abseil-cpp/absl/strings",
      ]
    }

    rtc_library("media_helper") {
      visibility = [ "*" ]
      testonly = true
      sources = [
        "media/media_helper.cc",
        "media/media_helper.h",
        "media/test_video_capturer_video_track_source.h",
      ]
      deps = [
        ":peer_configurer",
        ":test_peer",
        ":video_quality_analyzer_injection_helper",
        "../..:fileutils",
        "../..:platform_video_capturer",
        "../..:video_test_common",
        "../../../api:create_frame_generator",
        "../../../api:frame_generator_api",
        "../../../api:media_stream_interface",
        "../../../api:peer_connection_quality_test_fixture_api",
        "../../../api/video:video_frame",
        "../../../pc:peerconnection",
        "../../../pc:session_description",
        "../../../pc:video_track_source",
      ]
      absl_deps = [ "//third_party/abseil-cpp/absl/types:variant" ]
    }

    rtc_library("peer_configurer") {
      visibility = [ "*" ]
      testonly = true
      sources = [
        "peer_configurer.cc",
        "peer_configurer.h",
      ]
      deps = [
        ":peer_connection_quality_test_params",
        "../..:fileutils",
        "../../../api:callfactory_api",
        "../../../api:create_peer_connection_quality_test_frame_generator",
        "../../../api:fec_controller_api",
        "../../../api:packet_socket_factory",
        "../../../api:peer_connection_quality_test_fixture_api",
        "../../../api/rtc_event_log",
        "../../../api/task_queue",
        "../../../api/transport:network_control",
        "../../../api/video_codecs:video_codecs_api",
        "../../../rtc_base",
        "../../../rtc_base:threading",
      ]
      absl_deps = [ "//third_party/abseil-cpp/absl/strings" ]
    }

    rtc_library("test_activities_executor") {
      visibility = [ "*" ]
      testonly = true
      sources = [
        "test_activities_executor.cc",
        "test_activities_executor.h",
      ]
      deps = [
        "../../../api/units:time_delta",
        "../../../api/units:timestamp",
        "../../../rtc_base:checks",
        "../../../rtc_base:criticalsection",
        "../../../rtc_base:logging",
        "../../../rtc_base:rtc_base_approved",
        "../../../rtc_base:task_queue_for_test",
        "../../../rtc_base/synchronization:mutex",
        "../../../rtc_base/task_utils:repeating_task",
        "../../../system_wrappers",
      ]
      absl_deps = [
        "//third_party/abseil-cpp/absl/memory",
        "//third_party/abseil-cpp/absl/types:optional",
      ]
    }

    rtc_library("peerconnection_quality_test") {
      visibility = [ "*" ]
      testonly = true

      sources = [
        "peer_connection_quality_test.cc",
        "peer_connection_quality_test.h",
      ]
      deps = [
        ":analyzer_helper",
        ":cross_media_metrics_reporter",
        ":default_audio_quality_analyzer",
        ":default_video_quality_analyzer",
        ":media_helper",
        ":peer_configurer",
        ":peer_connection_quality_test_params",
        ":sdp_changer",
        ":single_process_encoded_image_data_injector",
        ":stats_poller",
        ":test_activities_executor",
        ":test_peer",
        ":test_peer_factory",
        ":video_quality_analyzer_injection_helper",
        ":video_quality_metrics_reporter",
        "../..:field_trial",
        "../..:fileutils",
        "../..:perf_test",
        "../../../api:audio_quality_analyzer_api",
        "../../../api:libjingle_peerconnection_api",
        "../../../api:media_stream_interface",
        "../../../api:peer_connection_quality_test_fixture_api",
        "../../../api:rtc_event_log_output_file",
        "../../../api:scoped_refptr",
        "../../../api:time_controller",
        "../../../api:video_quality_analyzer_api",
        "../../../api/rtc_event_log",
        "../../../api/task_queue",
        "../../../api/units:time_delta",
        "../../../api/units:timestamp",
        "../../../pc:pc_test_utils",
        "../../../pc:peerconnection",
        "../../../rtc_base",
        "../../../rtc_base:gunit_helpers",
        "../../../rtc_base:macromagic",
        "../../../rtc_base:rtc_base_approved",
        "../../../rtc_base:safe_conversions",
        "../../../rtc_base:task_queue_for_test",
        "../../../rtc_base:threading",
        "../../../rtc_base/synchronization:mutex",
        "../../../system_wrappers",
        "../../../system_wrappers:field_trial",
      ]
      absl_deps = [ "//third_party/abseil-cpp/absl/strings" ]
    }

    rtc_library("single_process_encoded_image_data_injector_unittest") {
      testonly = true
      sources = [
        "analyzer/video/single_process_encoded_image_data_injector_unittest.cc",
      ]
      deps = [
        ":single_process_encoded_image_data_injector",
        "../../../api/video:encoded_image",
        "../../../rtc_base:rtc_base_approved",
        "../../../test:test_support",
      ]
    }

    rtc_library("video_frame_tracking_id_injector_unittest") {
      testonly = true
      sources =
          [ "analyzer/video/video_frame_tracking_id_injector_unittest.cc" ]
      deps = [
        ":video_frame_tracking_id_injector",
        "../../../api/video:encoded_image",
        "../../../rtc_base:rtc_base_approved",
        "../../../test:test_support",
      ]
    }

    peer_connection_e2e_smoke_test_resources = [
      "../../../resources/pc_quality_smoke_test_alice_source.wav",
      "../../../resources/pc_quality_smoke_test_bob_source.wav",
    ]
    if (is_ios) {
      bundle_data("peer_connection_e2e_smoke_test_resources_bundle_data") {
        testonly = true
        sources = peer_connection_e2e_smoke_test_resources
        outputs = [ "{{bundle_resources_dir}}/{{source_file_part}}" ]
      }
    }

    rtc_library("peer_connection_e2e_smoke_test") {
      testonly = true

      sources = [ "peer_connection_e2e_smoke_test.cc" ]
      deps = [
        ":default_audio_quality_analyzer",
        ":default_video_quality_analyzer",
        ":network_quality_metrics_reporter",
        ":stats_based_network_quality_metrics_reporter",
        "../../../api:callfactory_api",
        "../../../api:create_network_emulation_manager",
        "../../../api:create_peer_connection_quality_test_frame_generator",
        "../../../api:create_peerconnection_quality_test_fixture",
        "../../../api:libjingle_peerconnection_api",
        "../../../api:media_stream_interface",
        "../../../api:network_emulation_manager_api",
        "../../../api:peer_connection_quality_test_fixture_api",
        "../../../api:scoped_refptr",
        "../../../api:simulated_network_api",
        "../../../api/audio_codecs:builtin_audio_decoder_factory",
        "../../../api/audio_codecs:builtin_audio_encoder_factory",
        "../../../api/video_codecs:builtin_video_decoder_factory",
        "../../../api/video_codecs:builtin_video_encoder_factory",
        "../../../call:simulated_network",
        "../../../media:rtc_audio_video",
        "../../../modules/audio_device:audio_device_impl",
        "../../../p2p:rtc_p2p",
        "../../../pc:pc_test_utils",
        "../../../pc:peerconnection_wrapper",
        "../../../rtc_base",
        "../../../rtc_base:gunit_helpers",
        "../../../rtc_base:logging",
        "../../../rtc_base:rtc_event",
        "../../../system_wrappers:field_trial",
        "../../../test:field_trial",
        "../../../test:fileutils",
        "../../../test:test_support",
      ]
      data = peer_connection_e2e_smoke_test_resources
      if (is_ios) {
        deps += [ ":peer_connection_e2e_smoke_test_resources_bundle_data" ]
      }
    }

    rtc_library("stats_poller") {
      visibility = [ "*" ]
      testonly = true
      sources = [
        "stats_poller.cc",
        "stats_poller.h",
      ]
      deps = [
        ":test_peer",
        "../../../api:libjingle_peerconnection_api",
        "../../../api:rtc_stats_api",
        "../../../api:stats_observer_interface",
        "../../../rtc_base:logging",
      ]
    }

    rtc_library("default_video_quality_analyzer_test") {
      testonly = true
      sources = [ "analyzer/video/default_video_quality_analyzer_test.cc" ]
      deps = [
        ":default_video_quality_analyzer",
        "../..:test_support",
        "../../../api:create_frame_generator",
        "../../../api:rtp_packet_info",
        "../../../api/video:encoded_image",
        "../../../api/video:video_frame",
        "../../../common_video",
        "../../../modules/rtp_rtcp:rtp_rtcp_format",
        "../../../rtc_base:stringutils",
        "../../../rtc_tools:video_quality_analysis",
        "../../../system_wrappers",
      ]
    }

    rtc_library("multi_head_queue_test") {
      testonly = true
      sources = [ "analyzer/video/multi_head_queue_test.cc" ]
      deps = [
        ":multi_head_queue",
        "../../../test:test_support",
      ]
      absl_deps = [ "//third_party/abseil-cpp/absl/types:optional" ]
    }
  }

  rtc_library("analyzer_helper") {
    visibility = [ "*" ]
    sources = [
      "analyzer_helper.cc",
      "analyzer_helper.h",
    ]
    deps = [
      "../../../api:sequence_checker",
      "../../../api:track_id_stream_info_map",
      "../../../rtc_base:macromagic",
    ]
    absl_deps = [ "//third_party/abseil-cpp/absl/strings" ]
  }

  rtc_library("default_audio_quality_analyzer") {
    visibility = [ "*" ]
    testonly = true
    sources = [
      "analyzer/audio/default_audio_quality_analyzer.cc",
      "analyzer/audio/default_audio_quality_analyzer.h",
    ]

    deps = [
      "../..:perf_test",
      "../../../api:audio_quality_analyzer_api",
      "../../../api:rtc_stats_api",
      "../../../api:stats_observer_interface",
      "../../../api:track_id_stream_info_map",
      "../../../api/numerics",
      "../../../api/units:time_delta",
      "../../../api/units:timestamp",
      "../../../rtc_base:criticalsection",
      "../../../rtc_base:logging",
      "../../../rtc_base:rtc_numerics",
      "../../../rtc_base/synchronization:mutex",
    ]
    absl_deps = [ "//third_party/abseil-cpp/absl/strings" ]
  }

  rtc_library("example_video_quality_analyzer") {
    visibility = [ "*" ]
    testonly = true
    sources = [
      "analyzer/video/example_video_quality_analyzer.cc",
      "analyzer/video/example_video_quality_analyzer.h",
    ]

    deps = [
      "../../../api:array_view",
      "../../../api:video_quality_analyzer_api",
      "../../../api/video:encoded_image",
      "../../../api/video:video_frame",
      "../../../api/video:video_rtp_headers",
      "../../../rtc_base:criticalsection",
      "../../../rtc_base:logging",
      "../../../rtc_base/synchronization:mutex",
    ]
  }

  rtc_library("video_quality_metrics_reporter") {
    visibility = [ "*" ]

    testonly = true
    sources = [
      "analyzer/video/video_quality_metrics_reporter.cc",
      "analyzer/video/video_quality_metrics_reporter.h",
    ]
    deps = [
      "../..:perf_test",
      "../../../api:peer_connection_quality_test_fixture_api",
      "../../../api:rtc_stats_api",
      "../../../api:track_id_stream_info_map",
      "../../../api/numerics",
      "../../../api/units:data_rate",
      "../../../api/units:data_size",
      "../../../api/units:time_delta",
      "../../../api/units:timestamp",
      "../../../rtc_base:criticalsection",
      "../../../rtc_base:rtc_numerics",
      "../../../rtc_base/synchronization:mutex",
    ]
    absl_deps = [ "//third_party/abseil-cpp/absl/strings" ]
  }

  rtc_library("default_video_quality_analyzer") {
    visibility = [ "*" ]

    testonly = true
    sources = [
      "analyzer/video/default_video_quality_analyzer.cc",
      "analyzer/video/default_video_quality_analyzer.h",
    ]

    deps = [
      ":multi_head_queue",
      "../..:perf_test",
      "../../../api:array_view",
      "../../../api:video_quality_analyzer_api",
      "../../../api/numerics",
      "../../../api/units:time_delta",
      "../../../api/units:timestamp",
      "../../../api/video:encoded_image",
      "../../../api/video:video_frame",
      "../../../api/video:video_rtp_headers",
      "../../../common_video",
      "../../../rtc_base:criticalsection",
      "../../../rtc_base:logging",
      "../../../rtc_base:rtc_base_approved",
      "../../../rtc_base:rtc_base_tests_utils",
      "../../../rtc_base:rtc_event",
      "../../../rtc_base:rtc_numerics",
      "../../../rtc_base:timeutils",
      "../../../rtc_base/synchronization:mutex",
      "../../../rtc_tools:video_quality_analysis",
      "../../../system_wrappers",
    ]
  }

  rtc_library("network_quality_metrics_reporter") {
    visibility = [ "*" ]
    testonly = true
    sources = [
      "network_quality_metrics_reporter.cc",
      "network_quality_metrics_reporter.h",
    ]
    deps = [
      "../..:perf_test",
      "../../../api:network_emulation_manager_api",
      "../../../api:peer_connection_quality_test_fixture_api",
      "../../../api:rtc_stats_api",
      "../../../api:track_id_stream_info_map",
      "../../../api/units:data_size",
      "../../../rtc_base:criticalsection",
      "../../../rtc_base:rtc_event",
      "../../../rtc_base/synchronization:mutex",
      "../../../system_wrappers:field_trial",
    ]
    absl_deps = [ "//third_party/abseil-cpp/absl/strings" ]
  }

  rtc_library("stats_based_network_quality_metrics_reporter") {
    visibility = [ "*" ]
    testonly = true
    sources = [
      "stats_based_network_quality_metrics_reporter.cc",
      "stats_based_network_quality_metrics_reporter.h",
    ]
    deps = [
      "../..:perf_test",
      "../../../api:array_view",
      "../../../api:network_emulation_manager_api",
      "../../../api:peer_connection_quality_test_fixture_api",
      "../../../api:rtc_stats_api",
      "../../../api:scoped_refptr",
      "../../../api/numerics",
      "../../../api/test/network_emulation",
      "../../../api/units:data_rate",
      "../../../api/units:data_size",
      "../../../api/units:timestamp",
      "../../../rtc_base",
      "../../../rtc_base:ip_address",
      "../../../rtc_base:rtc_event",
      "../../../rtc_base:stringutils",
      "../../../rtc_base/synchronization:mutex",
      "../../../system_wrappers:field_trial",
    ]
    absl_deps = [ "//third_party/abseil-cpp/absl/strings" ]
  }

  rtc_library("cross_media_metrics_reporter") {
    visibility = [ "*" ]
    testonly = true
    sources = [
      "cross_media_metrics_reporter.cc",
      "cross_media_metrics_reporter.h",
    ]
    deps = [
      "../..:perf_test",
      "../../../api:network_emulation_manager_api",
      "../../../api:peer_connection_quality_test_fixture_api",
      "../../../api:rtc_stats_api",
      "../../../api:track_id_stream_info_map",
      "../../../api/numerics",
      "../../../api/units:timestamp",
      "../../../rtc_base:criticalsection",
      "../../../rtc_base:rtc_event",
      "../../../rtc_base:rtc_numerics",
      "../../../rtc_base/synchronization:mutex",
      "../../../system_wrappers:field_trial",
    ]
    absl_deps = [
      "//third_party/abseil-cpp/absl/strings",
      "//third_party/abseil-cpp/absl/types:optional",
    ]
  }

  rtc_library("sdp_changer") {
    visibility = [ "*" ]
    testonly = true
    sources = [
      "sdp/sdp_changer.cc",
      "sdp/sdp_changer.h",
    ]
    deps = [
      "../../../api:array_view",
      "../../../api:libjingle_peerconnection_api",
      "../../../api:peer_connection_quality_test_fixture_api",
      "../../../api:rtp_parameters",
      "../../../media:rtc_media_base",
      "../../../p2p:rtc_p2p",
      "../../../pc:peerconnection",
      "../../../pc:rtc_pc_base",
      "../../../pc:session_description",
      "../../../pc:simulcast_description",
      "../../../rtc_base:stringutils",
    ]
    absl_deps = [
      "//third_party/abseil-cpp/absl/memory",
      "//third_party/abseil-cpp/absl/strings:strings",
      "//third_party/abseil-cpp/absl/types:optional",
    ]
  }

  rtc_library("multi_head_queue") {
    visibility = [ "*" ]
    testonly = true
    sources = [ "analyzer/video/multi_head_queue.h" ]
    deps = [ "../../../rtc_base:checks" ]
    absl_deps = [ "//third_party/abseil-cpp/absl/types:optional" ]
  }
}
