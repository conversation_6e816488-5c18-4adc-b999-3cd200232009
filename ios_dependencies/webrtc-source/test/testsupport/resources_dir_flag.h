/*
 *  Copyright (c) 2019 The WebRTC project authors. All Rights Reserved.
 *
 *  Use of this source code is governed by a BSD-style license
 *  that can be found in the LICENSE file in the root of the source
 *  tree. An additional intellectual property rights grant can be found
 *  in the file PATENTS.  All contributing project authors may
 *  be found in the AUTHORS file in the root of the source tree.
 */

#include <string>

#ifndef TEST_TESTSUPPORT_RESOURCES_DIR_FLAG_H__
#define TEST_TESTSUPPORT_RESOURCES_DIR_FLAG_H__

#include "absl/flags/declare.h"

ABSL_DECLARE_FLAG(std::string, resources_dir);

#endif  // TEST_TESTSUPPORT_RESOURCES_DIR_FLAG_H__
