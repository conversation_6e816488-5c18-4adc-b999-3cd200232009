# DEPS file for unit tests.

vars = {
  'chromium_git': 'https://chromium.googlesource.com',

  # This is updated compared to the DEPS file.
  'depot_tools_revision': '1206a353e40abb70d8454eb9af53db0ad10b713c',
}

deps = {
  'src/third_party/depot_tools':
    Var('chromium_git') + '/chromium/tools/depot_tools.git' + '@' +  Var('depot_tools_revision'),

  'src/third_party/xstream': {
      'packages': [
          {
              'package': 'chromium/third_party/xstream',
              # This is updated compared to the DEPS file.
              'version': 'version:1.10.0-cr0',
          },
      ],
      'condition': 'checkout_android',
      'dep_type': 'cipd',
  },

  # <PERSON>ript expects to find these markers.
  # === ANDROID_DEPS Generated Code Start ===
  # === ANDROID_DEPS Generated Code End ===
}
