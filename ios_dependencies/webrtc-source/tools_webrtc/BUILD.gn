# Copyright (c) 2020 The WebRTC project authors. All Rights Reserved.
#
# Use of this source code is governed by a BSD-style license
# that can be found in the LICENSE file in the root of the source
# tree. An additional intellectual property rights grant can be found
# in the file PATENTS.  All contributing project authors may
# be found in the AUTHORS file in the root of the source tree.

if (target_os == "android") {
  action("binary_version_check") {
    testonly = true
    script = "binary_version_check.py"
    deps = [ "../sdk/android:libjingle_peerconnection_so" ]
    inputs = [ "$root_out_dir/libjingle_peerconnection_so.so" ]
    outputs = [ "$root_out_dir/webrtc_binary_version_check" ]
    args = [ "libjingle_peerconnection_so.so" ]
  }
}
