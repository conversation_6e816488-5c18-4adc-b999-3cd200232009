<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
  xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
  <modelVersion>4.0.0</modelVersion>

  <groupId>org.webrtc</groupId>
  <artifactId>google-webrtc</artifactId>
  <version>{{ version }}</version>
  <packaging>aar</packaging>

  <name>Google's WebRTC Android library</name>
  <url>https://webrtc.org/</url>

  <properties>
    <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
    <project.build.commitid>{{ commit }}</project.build.commitid>
  </properties>

</project>
