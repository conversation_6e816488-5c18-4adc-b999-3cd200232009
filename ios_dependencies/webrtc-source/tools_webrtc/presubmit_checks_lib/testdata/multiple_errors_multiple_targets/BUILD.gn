# Copyright (c) 2017 The WebRTC project authors. All Rights Reserved.
#
# Use of this source code is governed by a BSD-style license
# that can be found in the LICENSE file in the root of the source
# tree. An additional intellectual property rights grant can be found
# in the file PATENTS.  All contributing project authors may
# be found in the AUTHORS file in the root of the source tree.

target("dummy_target_ok") {
  sources = [
    "dummy_source.cc",
    "dummy_source.h",
  ]
}

target("error_1") {
  sources = [
    "subpackage1/dummy_subpackage1.cc",
    "subpackage1/dummy_subpackage1.h",
  ]
}

target("error_2") {
  sources = [
    "subpackage1/dummy_subpackage2.cc",
    "subpackage1/dummy_subpackage2.h",
  ]
}
