# Copyright (c) 2020 The WebRTC project authors. All Rights Reserved.
#
# Use of this source code is governed by a BSD-style license
# that can be found in the LICENSE file in the root of the source
# tree. An additional intellectual property rights grant can be found
# in the file PATENTS.  All contributing project authors may
# be found in the AUTHORS file in the root of the source tree.

import("../../webrtc.gni")

if (rtc_enable_protobuf) {
  group("webrtc_dashboard_upload") {
    data = [ "webrtc_dashboard_upload.py" ]
    data_deps =
        [ "//third_party/catapult/tracing/tracing/proto:histogram_proto" ]
  }
}
