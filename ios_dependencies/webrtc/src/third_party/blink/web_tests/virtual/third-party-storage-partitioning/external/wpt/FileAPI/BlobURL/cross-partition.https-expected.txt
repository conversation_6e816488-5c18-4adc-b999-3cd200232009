This is a testharness.js-based test.
[FAIL] Blob URL shouldn't be revocable from a cross-partition iframe
  promise_test: Unhandled rejection with value: "Blob URL was revoked in not-same-top-level-site iframe: <PERSON><PERSON> failed"
[FAIL] Blob URL shouldn't be revocable from a cross-partition dedicated worker
  promise_test: Unhandled rejection with value: "Blob URL was revoked in not-same-top-level-site dedicated worker: <PERSON><PERSON> failed"
[FAIL] Blob URL shouldn't be revocable from a cross-partition shared worker
  promise_test: Unhandled rejection with value: "Blob URL was revoked in not-same-top-level-site shared worker: <PERSON><PERSON> failed"
[FAIL] Blob URL shouldn't be fetched from a cross-partition iframe
  promise_test: Unhandled rejection with value: "Blob URL was fetched in not-same-top-level-site iframe: <PERSON><PERSON> succeeded"
[FAIL] Blob URL shouldn't be fetched from a cross-partition dedicated worker
  promise_test: Unhandled rejection with value: "Blob URL was fetched in not-same-top-level-site dedicated worker: <PERSON><PERSON> succeeded"
[FAIL] Blob URL shouldn't be fetched from a cross-partition shared worker
  promise_test: Unhandled rejection with value: "Blob URL was fetched in not-same-top-level-site shared worker: <PERSON><PERSON> succeeded"
[FAIL] Blob URL shouldn't be fetched from a cross-partition service worker
  promise_test: Unhandled rejection with value: "Blob URL was fetched in not-same-top-level-site service worker: Fetch succeeded"
Harness: the test ran to completion.

