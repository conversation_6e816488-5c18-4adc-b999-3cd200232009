This is a testharness.js-based test.
[FAIL] Services workers under different top-level sites are partitioned.
  assert_true: The 1p iframe saw a pending promise in the service worker. expected true got false
[FAIL] Services workers with cross-site ancestors are partitioned.
  assert_true: The 1p iframe saw a pending promise in the service worker. expected true got false
Harness: the test ran to completion.

