<head>
    <script src="../../resources/ahem.js"></script>
    <style>
        div { width: 200px; font-family: Ahem; font-size: 40px; position: absolute; top: 0; left: 0; -webkit-font-smoothing: none; }
    </style>
</head>
<body style="position: relative;">
    <!-- There should be no red on the page. -->
    <div style="color: red;">AAAA B CCCC D <!-- -->E</div>
    <div id="target" style="color: green;">AAAA B CCCC D <!-- -->E</div>
    <script>
        var target = document.getElementById("target");
        target.removeChild(target.childNodes[1]);
        target.offsetTop;
        target.normalize();
        target.firstChild.splitText(0);
    </script>
</body>
