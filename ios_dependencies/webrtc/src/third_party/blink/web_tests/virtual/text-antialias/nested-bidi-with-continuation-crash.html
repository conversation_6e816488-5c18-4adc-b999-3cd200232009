<!DOCTYPE html>
<body onload="runTest();">
<script>
function reference(domNode)
{
    this.domNode = domNode;
}
function walk(a, currentPrefix, index, domNode)
{
    if (domNode == null)
        return;
    newPrefix = currentPrefix + "_" + index;
    walk(a, currentPrefix, index + 1, domNode.nextSibling);
    walk(a, newPrefix, 0, domNode.firstChild);
    a[newPrefix] = new reference(domNode);
}
function clearAllNodes()
{
    var a = new Array();
    walk(a, "", 0, document.body);
    for (key in a)
    {
        document.body.offsetTop;
        a[key].domNode.parentNode.removeChild(a[key].domNode);
    }
}
function runTest() {
    if (window.testRunner)
        testRunner.dumpAsText();
    clearAllNodes();
    document.write("Test passes if no crashes with asan.")
}
</script>
<output> <div> <pre> <div> </div></div>f<div> </div>
<output>o</output>
<a><u><textarea></textarea>
<br></br>
</u>
