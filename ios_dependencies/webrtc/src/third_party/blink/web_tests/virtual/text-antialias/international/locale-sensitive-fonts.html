<!DOCTYPE html>
<html>
<head>
<script>
if (window.internals) {
    internals.settings.setStandardFontFamily("<PERSON><PERSON>", "<PERSON>");
    internals.settings.setFantasyFontFamily("<PERSON><PERSON>", "<PERSON>");
}
</script>
</head>
<body>
<!-- This tests locale-sensitive font selection.  Using overridePreference,  the
fonts for Simplified Han are to Ahem font.  So all divs should match. -->
<div style="font-size: 20px">
<div style="font-family: 'Ahem'">this is ahem font</div>
<div style="-webkit-locale: 'zh_CN'">this is ahem font</div>
<div style="-webkit-locale: 'zh_CN'; font-family: fantasy">this is ahem font</div>
</div>
</body>
</html>
