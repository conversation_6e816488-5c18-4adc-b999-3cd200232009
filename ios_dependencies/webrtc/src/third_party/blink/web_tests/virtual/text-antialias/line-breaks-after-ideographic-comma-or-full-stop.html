<!DOCTYPE html>
<html>
    <head>
        <title>Line breaks after ideographic comma or full stop</title>
        <script src="../../resources/testharness.js"></script>
        <script src="../../resources/testharnessreport.js"></script>
    </head>
    <body>
        These are good:
        <div style="font-family:'Lucida Grande'; font-size:16pt; text-decoration:underline; width:5em;">
            <p style="border:solid green 1px;">
                &#x3046;&#x3001;<br>texttexttexttext
            </p>
            <p style="border:solid green 1px;">
                &#x3046;&#x3002;<br>texttexttexttext
            </p>
        </div>
        The following two should look like &ldquo;good&rdquo;:
        <div style="font-family:'Lucida Grande'; font-size:16pt; text-decoration:underline; width:5em;">
            <p style="border:solid blue 1px;">
                &#x3046;&#x3001;texttexttexttext
            </p>
            <p style="border:solid blue 1px;">
                &#x3046;&#x3002;texttexttexttext
            </p>
        </div>
        These are bad:
        <div style="font-family:'Lucida Grande'; font-size:16pt; text-decoration:underline; width:5em;">
            <p style="border:solid red 1px;">
                <nobr>&#x3046;&#x3001;texttexttexttext</nobr>
            </p>
            <p style="border:solid red 1px;">
                <nobr>&#x3046;&#x3002;texttexttexttext</nobr>
            </p>
        </div>
        <div id=result></div>
        <script>
            function paragraphHeight(paragraphNumber) {
                return document.defaultView.getComputedStyle(document.getElementsByTagName("p")[paragraphNumber], null).getPropertyValue("height")
            }

            test(() => {
              assert_true(paragraphHeight(0) == paragraphHeight(2));
              assert_true(paragraphHeight(1) == paragraphHeight(3));
            });
        </script>
    </body>
</html>
