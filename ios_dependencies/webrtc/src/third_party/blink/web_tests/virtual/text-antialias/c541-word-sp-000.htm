<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01//EN" "http://www.w3.org/TR/html4/strict.dtd">
<html>
 <head>
  <title>CSS Test: word-spacing</title>
  <meta name="flags" content="ahem">
  <script src="../../resources/ahem.js"></script>
  <link rel="help" href="http://www.w3.org/TR/REC-CSS1#word-spacing">
  <link rel="author" title="CSS1 Test Suite Contributors" href="http://www.w3.org/Style/CSS/Test/CSS1/current/tsack.html">
  <link rel="author" title="<PERSON>" href="mailto:<EMAIL>">
  <style type="text/css">
   div { font: 24px/1 Ahem; width: 18em; background: yellow; color: aqua; }
   .one {word-spacing: 0.25in;}
   .two {word-spacing: 0.635cm;}
   .three {word-spacing: 6.35mm;}
   .four {word-spacing: 18pt;}
   .five {word-spacing: 1.5pc;}
   .six {word-spacing: 1em;}
   .seven {word-spacing: 1.25ex;}
   .eight {word-spacing: 24px;}
  </style>
  <link rel="help" href="http://www.w3.org/TR/CSS21/text.html#spacing-props" title="16.4 Letter and word spacing: the 'letter-spacing' and 'word-spacing' properties">
 </head>
 <body>
  <p>There should be a stripy pattern of yellow and aqua below (each vertical stripe should be straight and unbroken).</p>
  <div class="test">x&nbsp;&nbsp;xx&nbsp;&nbsp;xxx&nbsp;&nbsp;xxxx</div>
  <div class="test">x&nbsp;&nbsp;xx&nbsp;&nbsp;xxx&nbsp;&nbsp;xxxx</div>
  <div class="test">x&nbsp;&nbsp;xx&nbsp;&nbsp;xxx&nbsp;&nbsp;xxxx</div>
  <div class="one"> x xx xxx xxxx </div>
  <div class="two"> x xx xxx xxxx </div>
  <div class="three"> x xx xxx xxxx </div>
  <div class="four"> x xx xxx xxxx </div>
  <div class="five"> x xx xxx xxxx </div>
  <div class="six"> x xx xxx xxxx </div>
  <div class="seven"> x xx xxx xxxx </div>
  <div class="eight"> x xx xxx xxxx </div>
 </body>
</html>