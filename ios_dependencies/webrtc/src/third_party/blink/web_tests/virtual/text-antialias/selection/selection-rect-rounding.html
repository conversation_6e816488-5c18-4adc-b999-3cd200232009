<style>
    div::selection { background-color: green; }
</style>
<div id="target" style="font: 11px 'lucida grande'; color: transparent; width: 100px; overflow: hidden;">i  x  m  n  o  p  q  r  s  t  u  v  w  x  y  z  i  x  m  n  o  p  q  r  s  t  u  v  w  x  y  z  i  x  m  n  o  p  q  r  s  t  u  v  w  x  y  z  i  x  m  n  o  p  q  r  s  t  u  v  w  x  y  z  i  x  m  n  o  p  q  r  s  t  u  v  w  x  y  z  i  x  m  n  o  p  q  r  s  t  u  </div>
<script>
    getSelection().setBaseAndExtent(target.firstChild, 0, target.firstChild, target.firstChild.length - 2);
</script>
