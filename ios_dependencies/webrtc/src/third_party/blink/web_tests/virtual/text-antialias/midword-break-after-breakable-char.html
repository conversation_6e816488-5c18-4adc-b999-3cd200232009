<head>
    <style>
        div { margin: 4px; width: 300px; background: lightyellow; word-wrap: break-word; float: left; }
    </style>
</head>
<body>
    <p>
        Test for <i><a href="http://bugs.webkit.org/show_bug.cgi?id=13156">http://bugs.webkit.org/show_bug.cgi?id=13156</a>
        REGRESSION (r19621): Pasting breakable content where wrapped line is too long to fit in a textarea fails to draw a horizontal scrollbar</i>.
    </p>
    <p>
        This tests that a line break will occur in the middle of the first word on a line if it&rsquo;s too long to fit on the line. The behavior is tested after breakable characters (question mark and hyphen), after a space and after a soft hyphen.
    </p>
    <p>
        The following blocks should be identical.
    </p>
    <div>
        Curabiturpretium,quamquiss?empermalesuada,estliberofeugiatlibero,velfringillaorcinibhsedneque-Quisqueeunullanonnisimolestieaccumsan.Etiamtellusurna,laore<PERSON>c,laore<PERSON>non suscipitsed,sapien.Phasellusvehicula,sematposuereve<PERSON>ula,auguenibhmolestienisl&shy;necullamcorperlacusantevulputatepede.Nasceturridiculusmus.
    </div>
    <div>
        Curabiturpretium,quamquiss?<br>
        empermalesuada,estliberofeugiatlibero,velfringi<br>
        llaorcinibhsedneque-<br>
        Quisqueeunullanonnisimolestieaccumsan.Etiam<br>
        tellusurna,laoreetac,laoreetnon<br>
        suscipitsed,sapien.Phasellusvehicula,sematposu<br>
        erevehicula,auguenibhmolestienisl-<br>
        necullamcorperlacusantevulputatepede.Nascetu<br>
        rridiculusmus.
    </div>
</body>
