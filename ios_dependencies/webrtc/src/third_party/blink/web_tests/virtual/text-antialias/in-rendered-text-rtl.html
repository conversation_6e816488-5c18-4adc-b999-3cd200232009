<html>
<head>
<title></title>
<script type="text/javascript">
function test()
{
    if (!window.testRunner)
        return;
    // Just click on the first (rightmost) word to get a response from the
    // editing delegate.
    var t = document.getElementById('t');
    eventSender.mouseMoveTo(t.offsetWidth - 8, t.offsetTop + 16);
    eventSender.mouseDown();
    eventSender.mouseUp();
}
</script>
</head>
<body onload="test()">
<p>
Test for <i><a href="https://bugs.webkit.org/show_bug.cgi?id=7433">http://bugzilla.opendarwin.org/show_bug.cgi?id=7433</a>
REGRESSION (r12789): Second RTL text run on a line cannot be selected</i>.
</p>
<p>
The rightmost two words in the Hebrew text should be selectable by dragging or double-clicking them.
</p>
<hr>
<span id="t">
&#x05d0;&#x05db;&#x05dc;&#x05ea &#x05e4;&#x05dc;&#x05e4;&#x05dc;  &#x05e9;&#x05ea;&#x05d4; &#x05de;&#x05d9;&#x05e5
</span>
</body>
</html>
