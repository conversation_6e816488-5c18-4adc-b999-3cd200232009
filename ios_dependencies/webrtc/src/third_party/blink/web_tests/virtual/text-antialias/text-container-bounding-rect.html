<html>
<head>
  <script src="../../resources/js-test.js"></script>
  <script>
    description('<a href="crbug.com/454760">Bug 454760</a>: getBoundingClientRect() on a container may be smaller than getBoundingClientRect() on the text it contains.')

    if (self.testRunner)
      self.testRunner.setTextSubpixelPositioning(true);

    window.onload = function() {
      divrect = document.getElementById("testdiv").getBoundingClientRect();
      spanrect = document.getElementById("testspan").getBoundingClientRect();
      shouldBeGreaterThanOrEqual("divrect.width", "spanrect.width");
    }
    </script>
</head>
<body>
  <div id="testdiv" style="width: -webkit-fit-content">
    <span id="testspan" style="font-size: 15px">Some text</span>
  </div>
</body>
</html>
