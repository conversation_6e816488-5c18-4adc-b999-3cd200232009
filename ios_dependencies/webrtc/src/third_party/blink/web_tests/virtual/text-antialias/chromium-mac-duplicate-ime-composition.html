<html>
<head>
  <script src="../../resources/run-after-layout-and-paint.js"></script>
  <script>
    function check(expect, actual) {
      var console = document.getElementById('console');
      var div = document.createElement('div');
      div.innerHTML = expect == actual ? 'PASS' : 'FAIL';
      div.innerHTML += ' expects = "' + expect + '", actual = "' + actual + '"';
      console.appendChild(div);
    }

    function doTest() {
      if (!window.testRunner)
        return;
      testRunner.dumpAsText();
      testRunner.waitUntilDone();
      var textarea = document.getElementById('textarea');
      var consoleElement = document.getElementById('console');
      textarea.addEventListener('keydown', function (e) {
        textarea.style['overflow'] = 'hidden';
      });
      runAfterLayoutAndPaint(function() {
        textarea.focus();
        if (textInputController.setComposition) {
          // Emulates start input method conversion.
          textInputController.setComposition('first');
          // Then, emulates change of the composition text of the input method.
          textInputController.setComposition('second');
          // Checks whether the textarea does not contain the first composition.
          check('second', textarea.value);
        }
        testRunner.notifyDone();
      });
    }
  </script>
</head>
<body onload="doTest()">
  <p>This page ensures that the composition text of an input method does not insert into the textarea when the composition text is updated but is not committed. The bug, reported in <a href="https://bugs.webkit.org/show_bug.cgi?id=46868">the issue 46868</a>, should not occur here. When DRT tests this page, it will emulate changing composition text from "first" to "second". After that, the value of the textarea should be "second" and should not be "secondfirst". </p>
  <p>For manual test, input text into the following textarea by using an input method. The composition text which is not committed should not be inserted into the textarea.</p>
  <textarea id="textarea"></textarea>
  <div id="console"></div>
</body>
</html>
