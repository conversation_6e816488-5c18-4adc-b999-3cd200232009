<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" 
"http://www.w3.org/TR/html4/loose.dtd">
<html>
<head>
<title>ATSUI spacing features</title>
<style type="text/css">
div.test { width: 200px; border: solid blue 1px; margin-bottom: 5px; }
div.rtl { direction: rtl; font-family: 'Lucida Grande'; line-height: 18px; }
div.ref { width: 200px; border: solid green 1px; }
div.word { word-spacing: 10px; }
div.letter { letter-spacing: 5px; }
div.justify { text-align: justify; }
</style>
</head>
<body>
<p>
Test for regressions against <i><a href="https://bugs.webkit.org/show_bug.cgi?id=3922">http://bugzilla.opendarwin.org/show_bug.cgi?id=3922</a> Variable word/letter spacing and full justification not supported for ATSUI-rendered text</i>.
</p>
Each green box should be identical to the blue box it follows, except for accents.
<hr>
<table>
<tr style="text-align: center;">
<td>Word spacing</td>
<td>Letter spacing</td>
<td>Justification</td>
</tr>
<tr style="vertical-align: top;">
<td>
<div class="word">
<div class="test rtl">
&#x05d9;&#x05b0;&#x05d4;&#x05b4;&#x05d9;,
&#x05d0;&#x05b8;&#x05d7;&#x05b4;&#x05d9;,
&#x05dc;&#x05b0;&#x05da;&#x05b8;
&#x05e1;&#x05b5;&#x05e4;&#x05b6;&#x05e8;
&#x05e9;&#x05c1;&#x05b0;&#x05dc;&#x05b7;&#x05d7;&#x05b0;&#x05ea;&#x05bc;&#x05b4;&#x05d9;&#x05d5;
&#x05d5;&#x05bc;&#x05de;&#x05b4;&#x05de;&#x05b0;&#x05db;&#x05bc;&#x05b6;&#x05e8;&#x05b6;&#x05ea; 
&#x05e6;&#x05b0;&#x05de;&#x05b4;&#x05d9;&#x05ea;&#x05d5;&#x05bc;&#x05ea;
&#x05dc;&#x05b8;&#x05da;&#x05b0;
&#x05de;&#x05b0;&#x05db;&#x05b7;&#x05e8;&#x05b0;&#x05ea;&#x05bc;&#x05b4;&#x05d9;&#x05d5;.
</div>
<div class="test">
Lore&#x0300;m ipsum dolor sit ame&#x0300;t, consectetuer adipiscing e&#x0300;lit.
</div>
<div class="ref">
Lorem ipsum dolor sit amet, consectetuer adipiscing elit.
</div>
</div>
</td>
<td>
<div class="letter">
<div class="test rtl">
&#x05d9;&#x05b0;&#x05d4;&#x05b4;&#x05d9;,
&#x05d0;&#x05b8;&#x05d7;&#x05b4;&#x05d9;,
&#x05dc;&#x05b0;&#x05da;&#x05b8;
&#x05e1;&#x05b5;&#x05e4;&#x05b6;&#x05e8;
&#x05e9;&#x05c1;&#x05b0;&#x05dc;&#x05b7;&#x05d7;&#x05b0;&#x05ea;&#x05bc;&#x05b4;&#x05d9;&#x05d5;
&#x05d5;&#x05bc;&#x05de;&#x05b4;&#x05de;&#x05b0;&#x05db;&#x05bc;&#x05b6;&#x05e8;&#x05b6;&#x05ea; 
&#x05e6;&#x05b0;&#x05de;&#x05b4;&#x05d9;&#x05ea;&#x05d5;&#x05bc;&#x05ea;
&#x05dc;&#x05b8;&#x05da;&#x05b0;
&#x05de;&#x05b0;&#x05db;&#x05b7;&#x05e8;&#x05b0;&#x05ea;&#x05bc;&#x05b4;&#x05d9;&#x05d5;.
</div>
<div class="test">
Lore&#x0300;m ipsum dolor sit ame&#x0300;t, consectetue&#x0300;r adipiscing e&#x0300;lit.
</div>
<div class="ref">
Lorem ipsum dolor sit amet, consectetuer adipiscing elit.
</div>
</div>
</td>
<td>
<div class="justify">
<div class="test rtl">
&#x05d9;&#x05b0;&#x05d4;&#x05b4;&#x05d9;,
&#x05d0;&#x05b8;&#x05d7;&#x05b4;&#x05d9;,
&#x05dc;&#x05b0;&#x05da;&#x05b8;
&#x05e1;&#x05b5;&#x05e4;&#x05b6;&#x05e8;
&#x05e9;&#x05c1;&#x05b0;&#x05dc;&#x05b7;&#x05d7;&#x05b0;&#x05ea;&#x05bc;&#x05b4;&#x05d9;&#x05d5;
&#x05d5;&#x05bc;&#x05de;&#x05b4;&#x05de;&#x05b0;&#x05db;&#x05bc;&#x05b6;&#x05e8;&#x05b6;&#x05ea; 
&#x05e6;&#x05b0;&#x05de;&#x05b4;&#x05d9;&#x05ea;&#x05d5;&#x05bc;&#x05ea;
&#x05dc;&#x05b8;&#x05da;&#x05b0;
&#x05de;&#x05b0;&#x05db;&#x05b7;&#x05e8;&#x05b0;&#x05ea;&#x05bc;&#x05b4;&#x05d9;&#x05d5;.
</div>
<div class="test">
Lore&#x0300;m ipsum dolor sit ame&#x0300;t, consectetue&#x0300;r adipiscing e&#x0300;lit.
</div>
<div class="ref">
Lorem ipsum dolor sit amet, consectetuer adipiscing elit.
</div>
</div>
</td>
</tr>
</table>
</body>
</html>
