<html>
<head></head>
<body bgcolor="#FFFFFF" text="#000000">
<h2>Safari Whitespace/Non-ASCII Bug</h2>
<p>Apart from whitespace (tab and linefeed characters), the two lists below (in red) use identical HTML. 
Both lists should be rendered on a single line. In Safari, however, the first list shows each item on a separate line.</p>
<p>This bug only seems to occur if the bullet character is non-ASCII (has a Unicode value higher than 127). Non-breaking spaces (&amp;#160;), however, are an exception.</p>
<br>
<font color="#CC0000">
&#187; England
							
							
							
							
							&#187; Ireland
							
							
							
							
							&#187; Scotland
							
							
							
							
							&#187; Germany
							
							
							
							
							&#187; France
<br>
<br>
&#187; England 
&#187; Ireland 
&#187; Scotland 
&#187; Germany 
&#187; France 
</font>
<br>
