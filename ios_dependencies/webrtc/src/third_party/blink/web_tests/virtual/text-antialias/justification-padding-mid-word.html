<script src="../../resources/ahem.js"></script>
<div id="test" style="width: 200px; font-family: Ahem; font-size: 40px; text-rendering: optimizelegibility; text-align: justify;">a&shy;b c de</div>
<div id="reference" style="width: 200px; font-family: Ahem; font-size: 40px; text-align: justify;">a&shy;b c de</div>
<div id="result"></div>
<script>
    if (window.testRunner)
        testRunner.dumpAsText();

    function widthOfFirstThreeCharacters(id)
    {
        var text = document.getElementById(id).firstChild;
        var range = document.createRange();
        range.setStart(text, 0);
        range.setEnd(text, 3);
        return range.getBoundingClientRect().width;
    }

    document.getElementById("result").innerText = widthOfFirstThreeCharacters("test") === widthOfFirstThreeCharacters("reference") ? "PASS" : "FAIL";
</script>
