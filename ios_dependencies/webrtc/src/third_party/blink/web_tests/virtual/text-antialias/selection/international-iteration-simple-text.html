<!DOCTYPE html>
<html>
    <head>
        <title>Simple text iteration test</title>
    </head>
    <body>
        <p>
            Test passes if it does not crash.
        </p>
        <script>
            if (window['testRunner'])
                testRunner.dumpAsText();

            var xs=[
                "el0=document.createElementNS('http://www.w3.org/2000/svg', 'svg'); document.body.appendChild(el0); ",
                 "el1=document.createElementNS('http://www.w3.org/2000/svg', 'svg'); ",
                 "el2=document.createElementNS('http://www.w3.org/2000/svg', 'text'); el0.appendChild(el2); ",
                 "document.body.appendChild(document.createTextNode(unescape('%ue029%ue36b%uebfb%uc23b%ua104%u7b74'))); ",
                 "el3=document.createElementNS('http://www.w3.org/2000/svg', 'symbol'); ",
                 "el5=document.createElementNS('http://www.w3.org/2000/svg', 'vkern'); ",
                 "el6=document.createElementNS('http://www.w3.org/2000/svg', 'a'); ",
                 "el7=document.createElementNS('http://www.w3.org/2000/svg', 'tspan'); el2.appendChild(el7); ",
                 "el8=document.createElementNS('http://www.w3.org/2000/svg', 'font'); ",
                 "el7.appendChild(document.createTextNode(unescape('%u937b%uf703%ub8bf%u0bce%uc2e8%u8d37'))); ",
                 "el10=document.createElementNS('http://www.w3.org/2000/svg', 'filter'); el10.setAttribute('enable-background', '29');el10.setAttribute('height', '96');",
                 "el11=document.createElementNS('http://www.w3.org/2000/svg', 'set'); el8.appendChild(el11); ",
                 "el12=document.createElement('keygen'); el11.parentNode.replaceChild(el12, el11);",
                 "el13=document.createElementNS('http://www.w3.org/2000/svg', 'marker'); ",
                 "el14=document.createElementNS('http://www.w3.org/2000/svg', 'path'); ",
                 "el15=document.createElementNS('http://www.w3.org/2000/svg', 'filter'); el15.setAttribute('enable-background', '169');",
                 "el7.appendChild(document.createTextNode(unescape('%uc65c%uae89%u90d9%ud9e8'))); ",
                 "el34=document.createElementNS('http://www.w3.org/2000/svg',  ",
                 "el131=document.createElementNS('http://www.w3.org/2000/svg',  ",
                 "el23.setAttribute('filter', 'url(#el10)')",
                 "window.getSelection().setBaseAndExtent(el3,0, 4)",
                 "setTimeout('window.close()', 100)"
            ];
            function next() {
                var xy=xs.shift();
                eval(xy);
                setTimeout(next, 0)
            }
            setTimeout(next, 0)
        </script>
    </body>
</html>
