<style>
  /*  This font contains a line glyph(U+0606) and a square glyph(U+06E1), and
   *  has a 'mark' feature that locates the square glyph above the line glyph.
   *  This font was created by using FontForge from scratch.
   */
  @font-face {
    font-family: test;
    src: url(resources/font-for-arabic-offset-test.ttf);
  }
  div {
    font-family: test;
    font-size: 48pt;
  }
</style>
<p>The square glyph should be located above the line glyph.</p>
<!-- Use arabic code points to make WebKit use ComplexTextController -->
<div>&#x0606;&#x06e1;</div>
<script>
if (window.testRunner) {
    testRunner.waitUntilDone();
    setTimeout(function() { testRunner.notifyDone(); }, 100);
}
</script>
