<!DOCTYPE>
<html>
    <head>
        <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
        <link rel="stylesheet" type="text/css" href="resources/text-scaling.css">
        <style>
            @font-face {
                font-family: 'Open Sans';
                font-style: normal;
                font-weight: 400;
                src: url('../../../resources/opensans/OpenSans-Regular.woff') format('woff');
            }
            section > div > div {
                font-family: 'Open Sans', 'Segoe UI';
            }
        </style>
        <script src="resources/text-scaling.js"></script>
        <script src="../../../resources/js-test.js"></script>
    </head>
    <body>
        <section>
            <h1>Font Size Scaling (WebFont, Latin)</h1>
            <p>
                Size of the text should scale smoothly.
                Reported width should be within 0.02px of that of the highlighted reference row.
            </p>
            <div id="test"></div>
        </section>
        <script>
            if (window.testRunner && testRunner.setTextSubpixelPositioning)
                testRunner.setTextSubpixelPositioning(true);

            var PANGRAM = 'My faxed joke won a pager in the cable TV quiz show.';

            jsTestIsAsync = true;
            // Prevent flakyness by explicitly loading the web font before measuring
            // the reference and test elements that are added to the page. Measuring
            // too early leads to the elements being rendered and measured
            // with "Times" before "Open Sans" is loaded, but Times on Windows is
            // not sub-pixel scaled at lower font sizes, causing the test to fail.
            document.fonts.load("12px Open Sans").then(testAndReport, notifyFail);

            function testAndReport() {
                var results = runTest(document.getElementById('test'), PANGRAM);

                if (results == PASS) {
                    testPassed('Size of text scales smoothly and width scales with font size as expected.');

                    // Hide text if test passes as the actual numbers are
                    // different across platforms and would require platform
                    // specific baselines.
                    if (window.testRunner)
                        document.getElementById('test').style.display = 'none';
                } else {
                    testFailed('Size of text does not scale smoothly, reported widths highlighted in red do not match reference row.');
                }
                finishJSTest();
            }

            function notifyFail() {
                testFailed("Unable to load web font.");
                finishJSTest();
            }
        </script>
    </body>
</html>
