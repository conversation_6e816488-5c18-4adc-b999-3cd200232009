<html>
<head>
<title></title>
<script type="text/javascript">
function test()
{
    var t = document.getElementById("t").firstChild;
    window.getSelection().setBaseAndExtent(t, 14, t, 19);
}
</script>
</head>
<body onload="test()">
<p>
Test for <i>http://bugzilla.opendarwin.org/show_bug.cgi?id=6132
Incorrect selection highlighting for ATSUI text when selected range is
"CG-safe"</i>.
</p>
<p>
The word &ldquo;dolor&rdquo; below should be highlighted in its entirety. The
highlight should not extend beyond that word.
</p>
<hr>
<span id="t">Lo&#x0308;re&#x0300;m ipsum dolor sit amet</span>
</body>
