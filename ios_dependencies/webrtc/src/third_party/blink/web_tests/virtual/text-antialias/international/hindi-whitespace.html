<html>
<head>
  <title>HTML 4.01/CSS 2.1 Test: white-space (for complex text)</title>
  <link rel="help" href="http://www.w3.org/TR/CSS21/text.html#white-space-prop" title="16.6 Whitespace: the 'white-space' property">
</head>
<body>
  <p>This HTML tests if the 'while-space' property controls the behavors of line-break characters (U+000A and U+000D) in a complex text (Hindi).</p>
  <p>If this test succeeds, you can see this Hindi word "&#x935;&#x93F;&#x937;&#x92F;&#x94B;&#x902;" repeated three times separated by a space in the first two
paragraphs. In the third paragraphs, it'll be shown three times in separate lines.</p>
  <p style="white-space: normal;">1. &#x935;&#x93F;&#x937;&#x92F;&#x94B;&#x902;&#x0A;&#x935;&#x93F;&#x937;&#x92F;&#x94B;&#x902;&#x0D;&#x935;&#x93F;&#x937;&#x92F;&#x94B;&#x902;</p>
  <p style="white-space: nowrap;">2. &#x935;&#x93F;&#x937;&#x92F;&#x94B;&#x902;&#x0A;&#x935;&#x93F;&#x937;&#x92F;&#x94B;&#x902;&#x0D;&#x935;&#x93F;&#x937;&#x92F;&#x94B;&#x902;</p>
  <p style="white-space: pre;">3.&#x0A;&#x935;&#x93F;&#x937;&#x92F;&#x94B;&#x902;&#x0A;&#x935;&#x93F;&#x937;&#x92F;&#x94B;&#x902;&#x0D;&#x935;&#x93F;&#x937;&#x92F;&#x94B;&#x902;</p>
 </body>
</html>
