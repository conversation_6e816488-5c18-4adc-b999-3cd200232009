<!DOCTYPE html>
<html>
<style>
body > div {
	display: inline-block;
	border: 1px solid black;
	width: 200px;
}
</style>
<body>
<p>This is a test for line layout to properly handle collapsing whitespace interspersed with inline isolated content.</p>
<div id="test">
<p>123
 <span style="unicode-bidi: -webkit-isolate">456</span> 789</p>
<p>123 <span style="unicode-bidi: -webkit-isolate">456</span> 789</p>
<p>123
 <span style="unicode-bidi: -webkit-isolate">4       56
</span> 789</p>
<p>123
 <span style="unicode-bidi: -webkit-isolate">
 456
 </span>
 789</p>
<p>1  2  3
 <span style="unicode-bidi: -webkit-isolate">
 4  5  6
 </span>
 7    8    9    </p>
</p>
<p>123
 <span style="unicode-bidi: -webkit-isolate"></span> 789</p>
<p>123
 <span style="unicode-bidi: -webkit-isolate">  </span> 789</p>
<p>123
<span style="unicode-bidi: -webkit-isolate"> 4       56 </span>
 789 <span style="unicode-bidi: -webkit-isolate">  10     11 12 </span> 13     14 15 </p>
<p>123
<span style="unicode-bidi: -webkit-isolate"> 4   <span style="unicode-bidi: -webkit-isolate">    5 6    7   </span>     89 </span>
    10 11 12 <span style="unicode-bidi: -webkit-isolate">  13     14 15  </p>

</div>
</body></html>
