<!DOCTYPE html>
<style>
#root {
  background-color: #ddd;
  font-size: 24pt;
  position: relative;
  width: 200px;
}
.flexbox {
  display: flex;
}
.flexbox * {
  width: 100px;
}
</style>

This test verifies that a selection spanning nested flexbox boundaries
is drawn correctly.

<div id="root">
  <div class="flexbox">
    <span id="start">aaa</span>
    <div class="flexbox"><span>bbb</span></div>
  </div>
  <div class="flexbox">
    <span>ccc</span>
    <div class="flexbox"><span id="end">ddd</span></div>
  </div>
</div>
<script>

var start = document.querySelector('#start'),
    end = document.querySelector('#end');
getSelection().setBaseAndExtent(start.firstChild, 1, end.firstChild, 2);
focus();

</script>
