<style>
.content {
  columns: 8;
}
p {
  margin: 6px 0em;
}
</style>
<p>
    Tests the resolved level of runs of neutral types.
</p>
<div class="content"">
<div style="direction: ltr;">
    <!-- ON run followed by L -->
    <p>
        <!-- L L ON ON L L -->
        ab(^cd
    </p>
    <p>
        <!-- R R ON ON L L -->
        &#x05d0;&#x05d1;(^cd
    </p>
    <p>
        <!-- AL AL ON ON L L -->
        &#x0627;&#x0628;(^cd
    </p>
    <p>
        <!-- L L EN EN ON ON L L -->
        ab12(^cd
    </p>
    <p>
        <!-- R R EN EN ON ON L L -->
        &#x05d0;&#x05d1;12(^cd
    </p>
    <p>
        <!-- AL AL EN EN ON ON L L -->
        &#x0627;&#x0628;12(^cd
    </p>
    <p>
        <!-- L L AN AN ON ON L L -->
        ab&#x0661;&#x0662;(^cd
    </p>
    <p>
        <!-- R R AN AN ON ON L L -->
        &#x05d0;&#x05d1;&#x0661;&#x0662;(^cd
    </p>
    <p>
        <!-- AL AL AN AN ON ON L L -->
        &#x0627;&#x0628;&#x0661;&#x0662;(^cd
    </p>
    <p>
        <!-- LRE ON PDF ON ON L L -->
        &#x202a;?&#x202c;(^cd
    </p>
    <p>
        <!-- RLE ON PDF ON ON L L -->
        &#x202b;?&#x202c;(^cd
    </p>
    
    <!-- ON run followed by R -->
    <p>
        <!-- L L ON ON R R -->
        ab(^&#x05d2;&#x05d3;
    </p>
    <p>
        <!-- R R ON ON R R -->
        &#x05d0;&#x05d1;(^&#x05d2;&#x05d3;
    </p>
    <p>
        <!-- AL AL ON ON R R -->
        &#x0627;&#x0628;(^&#x05d2;&#x05d3;
    </p>
    <p>
        <!-- L L EN EN ON ON R R -->
        ab12(^&#x05d2;&#x05d3;
    </p>
    <p>
        <!-- R R EN EN ON ON R R -->
        &#x05d0;&#x05d1;12(^&#x05d2;&#x05d3;
    </p>
    <p>
        <!-- AL AL EN EN ON ON R R -->
        &#x0627;&#x0628;12(^&#x05d2;&#x05d3;
    </p>
    <p>
        <!-- L L AN AN ON ON R R -->
        ab&#x0661;&#x0662;(^&#x05d2;&#x05d3;
    </p>
    <p>
        <!-- R R AN AN ON ON R R -->
        &#x05d0;&#x05d1;&#x0661;&#x0662;(^&#x05d2;&#x05d3;
    </p>
    <p>
        <!-- AL AL AN AN ON ON R R -->
        &#x0627;&#x0628;&#x0661;&#x0662;(^&#x05d2;&#x05d3;
    </p>
    <p>
        <!-- LRE ON PDF ON ON R R -->
        &#x202a;?&#x202c;(^&#x05d2;&#x05d3;
    </p>
    <p>
        <!-- RLE ON PDF ON ON R R -->
        &#x202b;?&#x202c;(^&#x05d2;&#x05d3;
    </p>

    <!-- ON run followed by AL -->
    <p>
        <!-- L L ON ON AL AL -->
        ab(^&#x062c;&#x062f;
    </p>
    <p>
        <!-- R R ON ON AL AL -->
        &#x05d0;&#x05d1;(^&#x062c;&#x062f;
    </p>
    <p>
        <!-- AL AL ON ON AL AL -->
        &#x0627;&#x0628;(^&#x062c;&#x062f;
    </p>
    <p>
        <!-- L L EN EN ON ON AL AL -->
        ab12(^&#x062c;&#x062f;
    </p>
    <p>
        <!-- R R EN EN ON ON AL AL -->
        &#x05d0;&#x05d1;12(^&#x062c;&#x062f;
    </p>
    <p>
        <!-- AL AL EN EN ON ON AL AL -->
        &#x0627;&#x0628;12(^&#x062c;&#x062f;
    </p>
    <p>
        <!-- L L AN AN ON ON AL AL -->
        ab&#x0661;&#x0662;(^&#x062c;&#x062f;
    </p>
    <p>
        <!-- R R AN AN ON ON AL AL -->
        &#x05d0;&#x05d1;&#x0661;&#x0662;(^&#x062c;&#x062f;
    </p>
    <p>
        <!-- AL AL AN AN ON ON AL AL -->
        &#x0627;&#x0628;&#x0661;&#x0662;(^&#x062c;&#x062f;
    </p>
    <p>
        <!-- LRE ON PDF ON ON AL AL -->
        &#x202a;?&#x202c;(^&#x062c;&#x062f;
    </p>
    <p>
        <!-- RLE ON PDF ON ON AL AL -->
        &#x202b;?&#x202c;(^&#x062c;&#x062f;
    </p>

    <!-- ON run followed by EN; additional R L added to show if the EN is at L+2 level -->
    <p>
        <!-- L L ON ON EN EN R L -->
        ab(^34&#x05e9;z
    </p>
    <p>
        <!-- R R ON ON EN EN R L -->
        &#x05d0;&#x05d1;(^34&#x05e9;z
    </p>
    <p>
        <!-- AL AL ON ON EN EN R L -->
        &#x0627;&#x0628;(^34&#x05e9;z
    </p>
    <p>
        <!-- L L EN EN ON ON EN EN R L -->
        ab12(^34&#x05e9;z
    </p>
    <p>
        <!-- R R EN EN ON ON EN EN R L -->
        &#x05d0;&#x05d1;12(^34&#x05e9;z
    </p>
    <p>
        <!-- AL AL EN EN ON ON EN EN R L -->
        &#x0627;&#x0628;12(^34&#x05e9;z
    </p>
    <p>
        <!-- L L AN AN ON ON EN EN R L -->
        ab&#x0661;&#x0662;(^34&#x05e9;z
    </p>
    <p>
        <!-- R R AN AN ON ON EN EN R L -->
        &#x05d0;&#x05d1;&#x0661;&#x0662;(^34&#x05e9;z
    </p>
    <p>
        <!-- AL AL AN AN ON ON EN EN R L -->
        &#x0627;&#x0628;&#x0661;&#x0662;(^34&#x05e9;z
    </p>
    <p>
        <!-- LRE ON PDF ON ON EN EN R L -->
        &#x202a;?&#x202c;(^34&#x05e9;z
    </p>
    <p>
        <!-- RLE ON PDF ON ON EN EN R L -->
        &#x202b;?&#x202c;(^34&#x05e9;z
    </p>

    <!-- ON run followed by AN; additional R L added to show if the AN is at L+2 level -->
    <p>
        <!-- L L ON ON AN AN R L -->
        ab(^&#x0663;&#x0664;&#x05e9;z
    </p>
    <p>
        <!-- R R ON ON AN AN R L -->
        &#x05d0;&#x05d1;(^&#x0663;&#x0664;&#x05e9;z
    </p>
    <p>
        <!-- AL AL ON ON AN AN R L -->
        &#x0627;&#x0628;(^&#x0663;&#x0664;&#x05e9;z
    </p>
    <p>
        <!-- L L EN EN ON ON AN AN R L -->
        ab12(^&#x0663;&#x0664;&#x05e9;z
    </p>
    <p>
        <!-- R R EN EN ON ON AN AN R L -->
        &#x05d0;&#x05d1;12(^&#x0663;&#x0664;&#x05e9;z
    </p>
    <p>
        <!-- AL AL EN EN ON ON AN AN R L -->
        &#x0627;&#x0628;12(^&#x0663;&#x0664;&#x05e9;z
    </p>
    <p>
        <!-- L L AN AN ON ON AN AN R L -->
        ab&#x0661;&#x0662;(^&#x0663;&#x0664;&#x05e9;z
    </p>
    <p>
        <!-- R R AN AN ON ON AN AN R L -->
        &#x05d0;&#x05d1;&#x0661;&#x0662;(^&#x0663;&#x0664;&#x05e9;z
    </p>
    <p>
        <!-- AL AL AN AN ON ON AN AN R L -->
        &#x0627;&#x0628;&#x0661;&#x0662;(^&#x0663;&#x0664;&#x05e9;z
    </p>
    <p>
        <!-- LRE ON PDF ON ON AN AN R L -->
        &#x202a;?&#x202c;(^&#x0663;&#x0664;&#x05e9;z
    </p>
    <p>
        <!-- RLE ON PDF ON ON AN AN R L -->
        &#x202b;?&#x202c;(^&#x0663;&#x0664;&#x05e9;z
    </p>

    <!-- ON run followed by LRE -->
    <p>
        <!-- L L ON ON LRE ON PDF -->
        ab(^&#x202a;!&#x202c;
    </p>
    <p>
        <!-- R R ON ON LRE ON PDF -->
        &#x05d0;&#x05d1;(^&#x202a;!&#x202c;
    </p>
    <p>
        <!-- AL AL ON ON LRE ON PDF -->
        &#x0627;&#x0628;(^&#x202a;!&#x202c;
    </p>
    <p>
        <!-- L L EN EN ON ON LRE ON PDF -->
        ab12(^&#x202a;!&#x202c;
    </p>
    <p>
        <!-- R R EN EN ON ON LRE ON PDF -->
        &#x05d0;&#x05d1;12(^&#x202a;!&#x202c;
    </p>
    <p>
        <!-- AL AL EN EN ON ON LRE ON PDF -->
        &#x0627;&#x0628;12(^&#x202a;!&#x202c;
    </p>
    <p>
        <!-- L L AN AN ON ON LRE ON PDF -->
        ab&#x0661;&#x0662;(^&#x202a;!&#x202c;
    </p>
    <p>
        <!-- R R AN AN ON ON LRE ON PDF -->
        &#x05d0;&#x05d1;&#x0661;&#x0662;(^&#x202a;!&#x202c;
    </p>
    <p>
        <!-- AL AL AN AN ON ON LRE ON PDF -->
        &#x0627;&#x0628;&#x0661;&#x0662;(^&#x202a;!&#x202c;
    </p>
    <p>
        <!-- LRE ON PDF ON ON LRE ON PDF -->
        &#x202a;?&#x202c;(^&#x202a;!&#x202c;
    </p>
    <p>
        <!-- RLE ON PDF ON ON LRE ON PDF -->
        &#x202b;?&#x202c;(^&#x202a;!&#x202c;
    </p>
    
    <!-- ON run followed by RLE -->
    <p>
        <!-- L L ON ON RLE ON PDF -->
        ab(^&#x202b;!&#x202c;
    </p>
    <p>
        <!-- R R ON ON RLE ON PDF -->
        &#x05d0;&#x05d1;(^&#x202b;!&#x202c;
    </p>
    <p>
        <!-- AL AL ON ON RLE ON PDF -->
        &#x0627;&#x0628;(^&#x202b;!&#x202c;
    </p>
    <p>
        <!-- L L EN EN ON ON RLE ON PDF -->
        ab12(^&#x202b;!&#x202c;
    </p>
    <p>
        <!-- R R EN EN ON ON RLE ON PDF -->
        &#x05d0;&#x05d1;12(^&#x202b;!&#x202c;
    </p>
    <p>
        <!-- AL AL EN EN ON ON RLE ON PDF -->
        &#x0627;&#x0628;12(^&#x202b;!&#x202c;
    </p>
    <p>
        <!-- L L AN AN ON ON RLE ON PDF -->
        ab&#x0661;&#x0662;(^&#x202b;!&#x202c;
    </p>
    <p>
        <!-- R R AN AN ON ON RLE ON PDF -->
        &#x05d0;&#x05d1;&#x0661;&#x0662;(^&#x202b;!&#x202c;
    </p>
    <p>
        <!-- AL AL AN AN ON ON RLE ON PDF -->
        &#x0627;&#x0628;&#x0661;&#x0662;(^&#x202b;!&#x202c;
    </p>
    <p>
        <!-- LRE ON PDF ON ON RLE ON PDF -->
        &#x202a;?&#x202c;(^&#x202b;!&#x202c;
    </p>
    <p>
        <!-- RLE ON PDF ON ON RLE ON PDF -->
        &#x202b;?&#x202c;(^&#x202b;!&#x202c;
    </p>

    <!-- ON run followed by PDF -->
    <p>
        <!-- LRE L L ON ON PDF -->
        &#x202a;ab(^&#x202c;
    </p>
    <p>
        <!-- LRE R R ON ON PDF -->
        &#x202a;&#x05d0;&#x05d1;(^&#x202c;
    </p>
    <p>
        <!-- LRE AL AL ON ON PDF -->
        &#x202a;&#x0627;&#x0628;(^&#x202c;
    </p>
    <p>
        <!-- LRE L L EN EN ON ON PDF -->
        &#x202a;ab12(^&#x202c;
    </p>
    <p>
        <!-- LRE R R EN EN ON ON PDF -->
        &#x202a;&#x05d0;&#x05d1;12(^&#x202c;
    </p>
    <p>
        <!-- LRE AL AL EN EN ON ON PDF -->
        &#x202a;&#x0627;&#x0628;12(^&#x202c;
    </p>
    <p>
        <!-- LRE L L AN AN ON ON PDF -->
        &#x202a;ab&#x0661;&#x0662;(^&#x202c;
    </p>
    <p>
        <!-- LRE R R AN AN ON ON PDF -->
        &#x202a;&#x05d0;&#x05d1;&#x0661;&#x0662;(^&#x202c;
    </p>
    <p>
        <!-- LRE AL AL AN AN ON ON PDF -->
        &#x202a;&#x0627;&#x0628;&#x0661;&#x0662;(^&#x202c;
    </p>
    <p>
        <!-- LRE LRE ON PDF ON ON PDF -->
        &#x202a;&#x202a;?&#x202c;(^&#x202c;
    </p>
    <p>
        <!-- LRE RLE ON PDF ON ON PDF -->
        &#x202a;&#x202b;?&#x202c;(^&#x202c;
    </p>
</div>

<div style="direction: rtl; text-align: left;">
    <!-- ON run followed by L -->
    <p>
        <!-- L L ON ON L L -->
        ab(^cd
    </p>
    <p>
        <!-- R R ON ON L L -->
        &#x05d0;&#x05d1;(^cd
    </p>
    <p>
        <!-- AL AL ON ON L L -->
        &#x0627;&#x0628;(^cd
    </p>
    <p>
        <!-- L L EN EN ON ON L L -->
        ab12(^cd
    </p>
    <p>
        <!-- R R EN EN ON ON L L -->
        &#x05d0;&#x05d1;12(^cd
    </p>
    <p>
        <!-- AL AL EN EN ON ON L L -->
        &#x0627;&#x0628;12(^cd
    </p>
    <p>
        <!-- L L AN AN ON ON L L -->
        ab&#x0661;&#x0662;(^cd
    </p>
    <p>
        <!-- R R AN AN ON ON L L -->
        &#x05d0;&#x05d1;&#x0661;&#x0662;(^cd
    </p>
    <p>
        <!-- AL AL AN AN ON ON L L -->
        &#x0627;&#x0628;&#x0661;&#x0662;(^cd
    </p>
    <p>
        <!-- LRE ON PDF ON ON L L -->
        &#x202a;?&#x202c;(^cd
    </p>
    <p>
        <!-- RLE ON PDF ON ON L L -->
        &#x202b;?&#x202c;(^cd
    </p>
    
    <!-- ON run followed by R -->
    <p>
        <!-- L L ON ON R R -->
        ab(^&#x05d2;&#x05d3;
    </p>
    <p>
        <!-- R R ON ON R R -->
        &#x05d0;&#x05d1;(^&#x05d2;&#x05d3;
    </p>
    <p>
        <!-- AL AL ON ON R R -->
        &#x0627;&#x0628;(^&#x05d2;&#x05d3;
    </p>
    <p>
        <!-- L L EN EN ON ON R R -->
        ab12(^&#x05d2;&#x05d3;
    </p>
    <p>
        <!-- R R EN EN ON ON R R -->
        &#x05d0;&#x05d1;12(^&#x05d2;&#x05d3;
    </p>
    <p>
        <!-- AL AL EN EN ON ON R R -->
        &#x0627;&#x0628;12(^&#x05d2;&#x05d3;
    </p>
    <p>
        <!-- L L AN AN ON ON R R -->
        ab&#x0661;&#x0662;(^&#x05d2;&#x05d3;
    </p>
    <p>
        <!-- R R AN AN ON ON R R -->
        &#x05d0;&#x05d1;&#x0661;&#x0662;(^&#x05d2;&#x05d3;
    </p>
    <p>
        <!-- AL AL AN AN ON ON R R -->
        &#x0627;&#x0628;&#x0661;&#x0662;(^&#x05d2;&#x05d3;
    </p>
    <p>
        <!-- LRE ON PDF ON ON R R -->
        &#x202a;?&#x202c;(^&#x05d2;&#x05d3;
    </p>
    <p>
        <!-- RLE ON PDF ON ON R R -->
        &#x202b;?&#x202c;(^&#x05d2;&#x05d3;
    </p>

    <!-- ON run followed by AL -->
    <p>
        <!-- L L ON ON AL AL -->
        ab(^&#x062c;&#x062f;
    </p>
    <p>
        <!-- R R ON ON AL AL -->
        &#x05d0;&#x05d1;(^&#x062c;&#x062f;
    </p>
    <p>
        <!-- AL AL ON ON AL AL -->
        &#x0627;&#x0628;(^&#x062c;&#x062f;
    </p>
    <p>
        <!-- L L EN EN ON ON AL AL -->
        ab12(^&#x062c;&#x062f;
    </p>
    <p>
        <!-- R R EN EN ON ON AL AL -->
        &#x05d0;&#x05d1;12(^&#x062c;&#x062f;
    </p>
    <p>
        <!-- AL AL EN EN ON ON AL AL -->
        &#x0627;&#x0628;12(^&#x062c;&#x062f;
    </p>
    <p>
        <!-- L L AN AN ON ON AL AL -->
        ab&#x0661;&#x0662;(^&#x062c;&#x062f;
    </p>
    <p>
        <!-- R R AN AN ON ON AL AL -->
        &#x05d0;&#x05d1;&#x0661;&#x0662;(^&#x062c;&#x062f;
    </p>
    <p>
        <!-- AL AL AN AN ON ON AL AL -->
        &#x0627;&#x0628;&#x0661;&#x0662;(^&#x062c;&#x062f;
    </p>
    <p>
        <!-- LRE ON PDF ON ON AL AL -->
        &#x202a;?&#x202c;(^&#x062c;&#x062f;
    </p>
    <p>
        <!-- RLE ON PDF ON ON AL AL -->
        &#x202b;?&#x202c;(^&#x062c;&#x062f;
    </p>

    <!-- ON run followed by EN; additional R L added to show if the EN is at L+2 level -->
    <p>
        <!-- L L ON ON EN EN R L -->
        ab(^34&#x05e9;z
    </p>
    <p>
        <!-- R R ON ON EN EN R L -->
        &#x05d0;&#x05d1;(^34&#x05e9;z
    </p>
    <p>
        <!-- AL AL ON ON EN EN R L -->
        &#x0627;&#x0628;(^34&#x05e9;z
    </p>
    <p>
        <!-- L L EN EN ON ON EN EN R L -->
        ab12(^34&#x05e9;z
    </p>
    <p>
        <!-- R R EN EN ON ON EN EN R L -->
        &#x05d0;&#x05d1;12(^34&#x05e9;z
    </p>
    <p>
        <!-- AL AL EN EN ON ON EN EN R L -->
        &#x0627;&#x0628;12(^34&#x05e9;z
    </p>
    <p>
        <!-- L L AN AN ON ON EN EN R L -->
        ab&#x0661;&#x0662;(^34&#x05e9;z
    </p>
    <p>
        <!-- R R AN AN ON ON EN EN R L -->
        &#x05d0;&#x05d1;&#x0661;&#x0662;(^34&#x05e9;z
    </p>
    <p>
        <!-- AL AL AN AN ON ON EN EN R L -->
        &#x0627;&#x0628;&#x0661;&#x0662;(^34&#x05e9;z
    </p>
    <p>
        <!-- LRE ON PDF ON ON EN EN R L -->
        &#x202a;?&#x202c;(^34&#x05e9;z
    </p>
    <p>
        <!-- RLE ON PDF ON ON EN EN R L -->
        &#x202b;?&#x202c;(^34&#x05e9;z
    </p>

    <!-- ON run followed by AN; additional R L added to show if the AN is at L+2 level -->
    <p>
        <!-- L L ON ON AN AN R L -->
        ab(^&#x0663;&#x0664;&#x05e9;z
    </p>
    <p>
        <!-- R R ON ON AN AN R L -->
        &#x05d0;&#x05d1;(^&#x0663;&#x0664;&#x05e9;z
    </p>
    <p>
        <!-- AL AL ON ON AN AN R L -->
        &#x0627;&#x0628;(^&#x0663;&#x0664;&#x05e9;z
    </p>
    <p>
        <!-- L L EN EN ON ON AN AN R L -->
        ab12(^&#x0663;&#x0664;&#x05e9;z
    </p>
    <p>
        <!-- R R EN EN ON ON AN AN R L -->
        &#x05d0;&#x05d1;12(^&#x0663;&#x0664;&#x05e9;z
    </p>
    <p>
        <!-- AL AL EN EN ON ON AN AN R L -->
        &#x0627;&#x0628;12(^&#x0663;&#x0664;&#x05e9;z
    </p>
    <p>
        <!-- L L AN AN ON ON AN AN R L -->
        ab&#x0661;&#x0662;(^&#x0663;&#x0664;&#x05e9;z
    </p>
    <p>
        <!-- R R AN AN ON ON AN AN R L -->
        &#x05d0;&#x05d1;&#x0661;&#x0662;(^&#x0663;&#x0664;&#x05e9;z
    </p>
    <p>
        <!-- AL AL AN AN ON ON AN AN R L -->
        &#x0627;&#x0628;&#x0661;&#x0662;(^&#x0663;&#x0664;&#x05e9;z
    </p>
    <p>
        <!-- LRE ON PDF ON ON AN AN R L -->
        &#x202a;?&#x202c;(^&#x0663;&#x0664;&#x05e9;z
    </p>
    <p>
        <!-- RLE ON PDF ON ON AN AN R L -->
        &#x202b;?&#x202c;(^&#x0663;&#x0664;&#x05e9;z
    </p>

    <!-- ON run followed by LRE -->
    <p>
        <!-- L L ON ON LRE ON PDF -->
        ab(^&#x202a;!&#x202c;
    </p>
    <p>
        <!-- R R ON ON LRE ON PDF -->
        &#x05d0;&#x05d1;(^&#x202a;!&#x202c;
    </p>
    <p>
        <!-- AL AL ON ON LRE ON PDF -->
        &#x0627;&#x0628;(^&#x202a;!&#x202c;
    </p>
    <p>
        <!-- L L EN EN ON ON LRE ON PDF -->
        ab12(^&#x202a;!&#x202c;
    </p>
    <p>
        <!-- R R EN EN ON ON LRE ON PDF -->
        &#x05d0;&#x05d1;12(^&#x202a;!&#x202c;
    </p>
    <p>
        <!-- AL AL EN EN ON ON LRE ON PDF -->
        &#x0627;&#x0628;12(^&#x202a;!&#x202c;
    </p>
    <p>
        <!-- L L AN AN ON ON LRE ON PDF -->
        ab&#x0661;&#x0662;(^&#x202a;!&#x202c;
    </p>
    <p>
        <!-- R R AN AN ON ON LRE ON PDF -->
        &#x05d0;&#x05d1;&#x0661;&#x0662;(^&#x202a;!&#x202c;
    </p>
    <p>
        <!-- AL AL AN AN ON ON LRE ON PDF -->
        &#x0627;&#x0628;&#x0661;&#x0662;(^&#x202a;!&#x202c;
    </p>
    <p>
        <!-- LRE ON PDF ON ON LRE ON PDF -->
        &#x202a;?&#x202c;(^&#x202a;!&#x202c;
    </p>
    <p>
        <!-- RLE ON PDF ON ON LRE ON PDF -->
        &#x202b;?&#x202c;(^&#x202a;!&#x202c;
    </p>
    
    <!-- ON run followed by RLE -->
    <p>
        <!-- L L ON ON RLE ON PDF -->
        ab(^&#x202b;!&#x202c;
    </p>
    <p>
        <!-- R R ON ON RLE ON PDF -->
        &#x05d0;&#x05d1;(^&#x202b;!&#x202c;
    </p>
    <p>
        <!-- AL AL ON ON RLE ON PDF -->
        &#x0627;&#x0628;(^&#x202b;!&#x202c;
    </p>
    <p>
        <!-- L L EN EN ON ON RLE ON PDF -->
        ab12(^&#x202b;!&#x202c;
    </p>
    <p>
        <!-- R R EN EN ON ON RLE ON PDF -->
        &#x05d0;&#x05d1;12(^&#x202b;!&#x202c;
    </p>
    <p>
        <!-- AL AL EN EN ON ON RLE ON PDF -->
        &#x0627;&#x0628;12(^&#x202b;!&#x202c;
    </p>
    <p>
        <!-- L L AN AN ON ON RLE ON PDF -->
        ab&#x0661;&#x0662;(^&#x202b;!&#x202c;
    </p>
    <p>
        <!-- R R AN AN ON ON RLE ON PDF -->
        &#x05d0;&#x05d1;&#x0661;&#x0662;(^&#x202b;!&#x202c;
    </p>
    <p>
        <!-- AL AL AN AN ON ON RLE ON PDF -->
        &#x0627;&#x0628;&#x0661;&#x0662;(^&#x202b;!&#x202c;
    </p>
    <p>
        <!-- LRE ON PDF ON ON RLE ON PDF -->
        &#x202a;?&#x202c;(^&#x202b;!&#x202c;
    </p>
    <p>
        <!-- RLE ON PDF ON ON RLE ON PDF -->
        &#x202b;?&#x202c;(^&#x202b;!&#x202c;
    </p>

    <!-- ON run followed by PDF -->
    <p>
        <!-- LRE L L ON ON PDF -->
        &#x202a;ab(^&#x202c;
    </p>
    <p>
        <!-- LRE R R ON ON PDF -->
        &#x202a;&#x05d0;&#x05d1;(^&#x202c;
    </p>
    <p>
        <!-- LRE AL AL ON ON PDF -->
        &#x202a;&#x0627;&#x0628;(^&#x202c;
    </p>
    <p>
        <!-- LRE L L EN EN ON ON PDF -->
        &#x202a;ab12(^&#x202c;
    </p>
    <p>
        <!-- LRE R R EN EN ON ON PDF -->
        &#x202a;&#x05d0;&#x05d1;12(^&#x202c;
    </p>
    <p>
        <!-- LRE AL AL EN EN ON ON PDF -->
        &#x202a;&#x0627;&#x0628;12(^&#x202c;
    </p>
    <p>
        <!-- LRE L L AN AN ON ON PDF -->
        &#x202a;ab&#x0661;&#x0662;(^&#x202c;
    </p>
    <p>
        <!-- LRE R R AN AN ON ON PDF -->
        &#x202a;&#x05d0;&#x05d1;&#x0661;&#x0662;(^&#x202c;
    </p>
    <p>
        <!-- LRE AL AL AN AN ON ON PDF -->
        &#x202a;&#x0627;&#x0628;&#x0661;&#x0662;(^&#x202c;
    </p>
    <p>
        <!-- LRE LRE ON PDF ON ON PDF -->
        &#x202a;&#x202a;?&#x202c;(^&#x202c;
    </p>
    <p>
        <!-- LRE RLE ON PDF ON ON PDF -->
        &#x202a;&#x202b;?&#x202c;(^&#x202c;
    </p>
</div>
</div>
