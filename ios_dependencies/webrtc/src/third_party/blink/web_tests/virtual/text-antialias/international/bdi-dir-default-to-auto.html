<!DOCTYPE html>
<html>
  <head>
    <meta charset="utf-8">
    <title>HTML Test: BDI: paragraph-level container</title>
    <link rel="reference" href="bdi-auto-dir-default-ref.html">
    <link rel="author" title="<PERSON><PERSON><PERSON>" href="mailto:<EMAIL>">
    <link rel="author" title="HTML5 bidi test WG" href="mailto:<EMAIL>">
    <link rel="help" href="http://dev.w3.org/html5/spec/Overview.html#the-bdi-element">
    <meta name="assert" content="
      'The dir global attribute defaults to auto on this element (it never inherits from the parent
      element like with other elements).'">
    <style>
      body{
        font-size:2em;
      }
      .box {
        border: medium solid gray;
        width: 500px;
        margin: 20px;
      }
    </style>
  </head>
  <body>
    The two boxes below should look exactly the same.
    <!-- Key to entities used below:
      &#x05D0; ... &#x05D5; - The first six Hebrew letters (strongly RTL).
      &#x202D; - The LRO (left-to-right-override) formatting character.
      &#x202C; - The PDF (pop directional formatting) formatting character; closes LRO. -->
    <div class="box">
      <!--
        In each DIV below:
        - the first BDI, having no characters with strong direction, should be LTR by default;
        - the second BDI, having an LTR character first, should be LTR by default;
        - the third BDI, having an RTL character first, should be RTL by default.
      -->
      <div dir="ltr"><bdi>[:)]</bdi>, <bdi>[+- a &#x05D1;]</bdi>, <bdi>[1 &#x05D2; d]</bdi>...</div>
      <div dir="rtl"><bdi>[:)]</bdi>, <bdi>[+- a &#x05D1;]</bdi>, <bdi>[1 &#x05D2; d]</bdi>...</div>
    </div>
    <div class="box">
      <div dir="ltr">&#x202D;[:)], [+- a &#x05D1;], [d &#x05D2; 1]...&#x202C;</div>
      <div dir="rtl">&#x202D;...[d &#x05D2; 1] ,[+- a &#x05D1;] ,[:)]&#x202C;</div>
    </div>
  </body>
</html>
