<!DOCTYPE html>
<html>
    <head>
        <title>Line breaks after closing punctuations</title>
        <script src="../../resources/testharness.js"></script>
        <script src="../../resources/testharnessreport.js"></script>
    </head>
    <body>
        These are good:
        <div style="font-size:16pt; text-decoration:underline; width:5em;">
            <p style="border:solid green 1px;">
                (111111)<br>(222222)
            </p>
            <p style="border:solid green 1px;">
                [111111]<br>[222222]
            </p>
        </div>
        </div>
        The following two should look like &ldquo;good&rdquo;:
        <div style="font-size:16pt; text-decoration:underline; width:5em;">
            <p style="border:solid blue 1px;">
                (111111)(222222)
            </p>
            <p style="border:solid blue 1px;">
                [111111][222222]
            </p>
        </div>
        These are bad:
        <div style="font-size:16pt; text-decoration:underline; width:5em;">
            <p style="border:solid red 1px;">
                <nobr>(111111)(222222)</nobr>
            </p>
            <p style="border:solid red 1px;">
                <nobr>[111111][222222]</nobr>
            </p>
        </div>
        <div id=result></div>
        <script>
            function paragraphHeight(paragraphNumber) {
                return document.defaultView.getComputedStyle(document.getElementsByTagName("p")[paragraphNumber], null).getPropertyValue("height")
            }

test(() => {
  assert_equals(paragraphHeight(0), paragraphHeight(2));
  assert_equals(paragraphHeight(1), paragraphHeight(3));
});
        </script>
    </body>
</html>
