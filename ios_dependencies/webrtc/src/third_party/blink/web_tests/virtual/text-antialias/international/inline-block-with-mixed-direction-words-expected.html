<!DOCTYPE html>
<html>
    <head>
        <style>
            span {
                display: inline-block;
                font: menu;
                background: pink;
                line-height: 1.5em;
                overflow: hidden;
                position: absolute;
                top: 50px;
                border: 1px solid white;
            }
        </style>
    </head>
    <body>
        <p>
            The pink background should extend the full width of the
            two blocks.
        </p>
        <span>AB&#x0627;&#x0628;</span> <span>12&#x0627;|&#x0628;:&nbsp;</span>
    </body>
</html>
