<div style="outline: thin dashed lightblue;">
    I <span id="could-not">couldn&rsquo;t</span> tell if she tru&shy;ly ad&shy;vo&shy;cat&shy;ed anti&shy;dis&shy;est&shy;ab&shy;lish&shy;ment&shy;arian&shy;ism.
</div>
<script>
    var couldNot = document.getElementById("could-not");
    var width = couldNot.getBoundingClientRect().width;
    couldNot.parentElement.style.width = Math.ceil(width);
</script>
