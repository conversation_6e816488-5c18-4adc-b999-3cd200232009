<!DOCTYPE html>
<style>
a.trigger {
  width: 337px;
  height: 16px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  display: inline-block;
  border: 1px solid;
}

div{
  height: 16px;
  width: 32px;
  float: left;
}

</style>
<p>crbug.com/275883: Restore position of line box correctly after deleting ellipsis.</p>
<a class="trigger">
  <div></div>
  <span id="span">Some text</span>
</a>
<input type="button" id="input" value="Change Text" />
<script>
var toggle = 0;
input.onclick =  function (e) {
  var text = (toggle) ? 'Really really really long text that is so long long really long' : 'This is shorter text';
  span.textContent = text;
  toggle = (toggle) ? 0 : 1;
};
document.body.offsetTop;
eventSender.mouseMoveTo(355, 55);
eventSender.mouseDown();
eventSender.mouseUp();
eventSender.mouseDown();
eventSender.mouseUp();
eventSender.mouseDown();
eventSender.mouseUp();
eventSender.mouseMoveTo(155, 55);
eventSender.mouseDown();
eventSender.mouseUp();
</script>
