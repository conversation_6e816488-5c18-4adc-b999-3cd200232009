<!DOCTYPE html>
<style type="text/css">
.container {
    font-size: 200px;
    line-height: 1;
}

@font-face {
    /* LinLibertine_R's aspect value = 0.440918 on mac */
    font-family : 'referenceFont';
    src : url('../../third_party/Libertine/LinLibertine_R.woff') format("woff");
}

@font-face {
    /* OpenSans-Regular's aspect = 0.544922 on mac */
    /* OpenSans-Regular's aspect = 0.535156 on linux */
    font-family : 'testFont';
    src : url('../../resources/opensans/OpenSans-Regular.woff') format("woff");
}

.test {
    font-family: testFont;
    font-size-adjust: 0.440;
}

.testNone {
    font-family: testFont;
    font-size-adjust: none;
}

.reference {
    font-family: referenceFont;
}
</style>

<div class="container">
    <span class="test">x</span><span class="reference">x</span>
</div>
<div class="container">
    <span class="testNone">z</span><span class="reference">z</span>
</div>
