<!DOCTYPE html>
<script src="../../resources/testharness.js"></script>
<script src="../../resources/testharnessreport.js"></script>
<style>
  caption:first-line { visibility: hidden; }
  caption:first-letter { color: red; }
</style>
<p>
  The test passes if it does not CRASH in debug builds.
  <meta id="meta"/>
  <object id="object">
    <table>
      <caption dir="rtl">
        )TQ?
      </caption>
    </table>
  </object>
</p>
<p></p>
<script>
test(() => {
  document.getElementById('object').appendChild(document.getElementById('meta'));
  document.execCommand('FindString', 'test');
}, 'Should not crash');
</script>
