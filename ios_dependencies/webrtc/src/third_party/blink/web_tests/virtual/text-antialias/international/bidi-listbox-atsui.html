<style>
.bidi2 {
    direction: rtl; 
    -webkit-rtl-ordering: logical;
    max-width: 100px;
}
.bidi4 {
    direction: ltr; 
    -webkit-rtl-ordering: logical;
    max-width: 100px;
}

.bidi5 {
    max-width: 100px;
}
</style>
<p>
This tests that bidirectional text is correctly rendered when using ATSUI in list box controls.<br>
The order of the text below each list box should match the order of the select's option text.
</p>
    1) direction: rtl;<br>
<select size="2" class="bidi2"><option>a&#x0300;bc&#x05d0;&#x05e4;&#x05e8;&#x05e1;&#x05de;&#x05d5;&#x05df;</option></select><br>
<div class="bidi2">a&#x0300;bc&#x05d0;&#x05e4;&#x05e8;&#x05e1;&#x05de;&#x05d5;&#x05df;</div><br>
    2) direction: ltr;<br>
<select size="2" class="bidi4"><option>a&#x0300;bc&#x05d0;&#x05e4;&#x05e8;&#x05e1;&#x05de;&#x05d5;&#x05df;</option></select><br>
<div class="bidi4">a&#x0300;bc&#x05d0;&#x05e4;&#x05e8;&#x05e1;&#x05de;&#x05d5;&#x05df;</div><br>
    3) No style<br>
<select size="2" class="bidi5"><option>a&#x0300;bc&#x05d0;&#x05e4;&#x05e8;&#x05e1;&#x05de;&#x05d5;&#x05df;</option></select><br>
<div class="bidi5">a&#x0300;bc&#x05d0;&#x05e4;&#x05e8;&#x05e1;&#x05de;&#x05d5;&#x05df;</div><br>
