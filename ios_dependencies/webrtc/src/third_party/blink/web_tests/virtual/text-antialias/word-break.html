This example tests that word-break properly shrinks a cell to one character per line when it is set to break-all.

<table style="font-family: monospace" width=100% border=1 cellpadding=5 cellspacing=0>
<tr><td style="word-break: break-all">Narrow<td width=100%>This cell should grow
</table>

<br>

This test checks that word-break is ignored when white-space is pre.

<pre style="width:100px; border:5px solid black; word-break: break-all">
Thislongwordshouldnotgetbrokenbutshouldstickoutofthepre.
</pre>

<br>

The next two examples test our custom break-word value for word-break.  We should get the same results as
break-all for the first test, but we should break up the word in the second test (since our behavior matches
word-wrap: break-word in that case).

<table style="font-family: monospace" width=100% border=1 cellpadding=5 cellspacing=0>
<tr><td style="word-break: break-word">Narrow<td width=100%>This cell should grow
</table>

<br>

<pre style="width:100px; border:5px solid black; word-break: break-word">
Thislongwordshouldnotgetbrokenbutshouldstickoutofthepre.
</pre>

<br>

This test makes sure floating and positioned elements do the right thing with word-break: break-all.  The minimum width
should be less than the containing block width.  The breaking should be "dumb" and just occur when the end of the line is hit.

<div style="width:300px; border:2px solid black; font-family: monospace; word-break: break-all">
<div style="float:left">Eventhoughthishasareallylongword itshouldnotstick out oftheenclosing300pxblock.</div>
<br clear="all">
</div>

<br>

<div style="width:300px; position:relative; border:2px solid black; font-family: monospace; word-break: break-all; height:100px">
<div style="position:absolute">Eventhoughthishasareallylongword itshouldnotstick out oftheenclosing300pxblock.</div>
</div>

<br>

This test makes sure floating and positioned elements do the right thing with word-break: break-word.  The minimum width
should be less than the containing block width.  The breaking should be "smart" and be like word-wrap: break-word, with words
preferring to wrap first before allowing break opportunities within them.

<div style="width:300px; border:2px solid black; font-family: monospace; word-break: break-word">
<div style="float:left">Eventhoughthishasareallylongword itshouldnotstick out oftheenclosing300pxblock.</div>
<br clear="all">
</div>

<br>

<div style="width:300px; position:relative; border:2px solid black; font-family: monospace; word-break: break-word; height:100px">
<div style="position:absolute">Eventhoughthishasareallylongword itshouldnotstick out oftheenclosing300pxblock.</div>
</div>
