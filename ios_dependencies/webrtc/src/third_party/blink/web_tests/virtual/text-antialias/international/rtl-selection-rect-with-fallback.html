<!DOCTYPE html>
<meta charset="utf-8">
<script src="../../../resources/testharness.js"></script>
<script src="../../../resources/testharnessreport.js"></script>
<style>
div {
  /* Must be a font that causes fallback for Arabic.
    'MS PGothic' for Windows, TakaoPGothic for Linux, Times for Mac. */
  font-family: 'MS PGothic', TakaoPGothic, Times;
}

.PASS {
  background-color: lime;
}

.FAIL {
  background-color: red;
}
</style>
<div id="container">
  <div id="ltr">'ص</div>
  <div id="rtl" dir="rtl">'ص</div>
</div>
<script>
Array.prototype.forEach.call(container.children, function (element) {
  test(function () {
    var textNode = element.firstChild;
    var width0 = getWidth(textNode, 0);
    var width1 = getWidth(textNode, 1);
    element.className = width0 < width1 ? "PASS" : "FAIL";
    assert_less_than(width0, width1);
  }, element.id);
});

function getWidth(node, offset) {
  var range = document.createRange();
  range.setStart(node, offset);
  range.setEnd(node, offset + 1);
  var rect = range.getBoundingClientRect();
  return rect.width;
}

if (window.testRunner)
  container.style.display = "none";
</script>
