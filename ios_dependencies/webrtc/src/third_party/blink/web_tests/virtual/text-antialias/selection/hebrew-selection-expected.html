<!DOCTYPE html>
<html>
<body>

<!-- To calculate width of composed glyph -->
<div style="font-size:500%">
<span id="reference">&#x5e9;&#x5b0;</span>
</div>

<p>U+05e9 U+05b0 of following text should be selected.</p>
<div style="font-size:500%">
<span id="target">&#x5e1;&#x5b0;&#x5e9;&#x5b0;</span>
</div>

<script>
var target = document.getElementById("target");
var text = target.firstChild;
window.getSelection().setBaseAndExtent(text, 2, text, 4);
target.focus();
</script>
</body>
</html>
