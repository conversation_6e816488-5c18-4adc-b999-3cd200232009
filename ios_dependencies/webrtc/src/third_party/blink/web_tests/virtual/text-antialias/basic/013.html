<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN"
    "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">

<html xmlns="http://www.w3.org/1999/xhtml">
  <head>
    <meta http-equiv="content-type" content="text/html;charset=ISO-8859-1" />

    <title>Letter spacing lost for emphased text</title>
  </head>

  <body>
    <h1>Letter spacing lost for emphasized text</h1>

    <p>This example demonstrates that text inside a <code>b</code>, <code>i</code>, <code>strong</code> or <code>em</code> element loses its <code>letter-spacing</code> CSS attribute that should be inherited. This was discovered in Safari 1.0.</p>

    <div style="letter-spacing: 0.3em">
      <p>Text inside <b>bold</b> element.</p>

      <p>Text inside <strong>strong</strong> element.</p>

      <p>Text inside <i>italic</i> element.</p>

      <p>Text inside <em>emphasis</em> element.</p>
    </div>
  </body>
</html>

