<head>
    <style>
        div { float: left; clear: both; border:solid ; word-spacing: 50px; }
    </style>
</head>
<body>
    <p>
        Test for <i><a href="http://bugs.webkit.org/show_bug.cgi?id=15259">http://bugs.webkit.org/show_bug.cgi?id=15259</a>
        REGRESSION:Text overflows if a empty &lttd> is followed by a &lt;td align="center"></i>.
    </p>
    <p>
        The next 7 lines should all look the same.
    </p>
    <div>  A B  </div>
    <div> A B </div>
    <div><span>A</span> B</div>
    <div><span>A</span> B</div>
    <div style="text-align: right;"><span>A</span> B</div>
    <div style="text-align: right;"><span>A</span> <span>B</span></div>
    <div style="text-align: right;"><span>A</span> B</div>
</body>
