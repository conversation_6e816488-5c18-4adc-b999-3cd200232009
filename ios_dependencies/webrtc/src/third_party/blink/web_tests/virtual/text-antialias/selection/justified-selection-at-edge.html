<style>
    .fakeselection, ::selection { color: green; background-color: yellow; }
</style>
<p>
    Test for bug 13234, layout of selected justified text is broken.
</p>
<p>
    The two blue boxes should be identical.
</p>
<div style="text-align: justify; width: 250px; border: solid blue;">Lorem ipsum dolor si<span class="fakeselection">t amet,</span> consectetuer adipiscing elit.
 </div>
<br>
<p align="justify" id="target" style="width: 250px; border: solid blue">Lorem ipsum dolor sit amet, consectetuer adipiscing elit.</p>
<script>
    var text = document.getElementById("target").firstChild;
    getSelection().setBaseAndExtent(text, 27, text, 20);
</script>
