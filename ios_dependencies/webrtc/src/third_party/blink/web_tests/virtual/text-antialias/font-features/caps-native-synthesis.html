<!DOCTYPE HTML>
<html>
<head>
<title>font-variant-caps fallback</title>
<meta charset="UTF-8">
<style>

/* Libertine only supports smcp/c2sc, not pcap/c2pc */
@font-face {
  font-family: libertine;
  src: url(../../../third_party/Libertine/LinLibertine_R.woff);
}

body {
margin: 20px;
font-size: 20px;
}

.libertine {
font-family: libertine, sans-serif;
}

.sansserif {
font-family: sans-serif;
}

.titling { font-variant-caps: titling-case; }
.smallcaps { font-variant-caps: small-caps; }
.allsmallcaps { font-variant-caps: all-small-caps; }
.petitecaps { font-variant-caps: petite-caps; }
.allpetitecaps { font-variant-caps: all-petite-caps; }
.unicase { font-variant-caps: unicase; }
</style>
</head>
<body>
<p>The following lines should display as indicated at the beginning of the line, whether the font has full support,
    partial support for the corresponding feature or not, in which case it is synthesized (except for titling, no
    synthesis there).</span>
<h3>Sans-serif</h3>
<div class="sansserif">
titling: <span class=titling>Aa Bb Gg Δδ Γγ Σσ Бб Фф</span><br>
smallcaps: <span class=smallcaps>Aa Bb Gg Δδ Γγ Σσ Бб Фф</span><br>
allsmallcaps: <span class=allsmallcaps>Aa Bb Gg Δδ Γγ Σσ Бб Фф</span><br>
petitecaps: <span class=petitecaps>Aa Bb Gg Δδ Γγ Σσ Бб Фф</span><br>
allpetitecaps: <span class=allpetitecaps>Aa Bb Gg Δδ Γγ Σσ Бб Фф</span><br>
unicase: <span class=unicase>Aa Bb Gg Δδ Γγ Σσ Бб Фф</span><br>
</div>
    <h3>Libertine (has native pcap and c2sc support)</h3>
<div class="libertine">
titling: <span class=titling>Aa Bb Gg Δδ Γγ Σσ Бб Фф</span><br>
smallcaps: <span class=smallcaps>Aa Bb Gg Δδ Γγ Σσ Бб Фф</span><br>
allsmallcaps: <span class=allsmallcaps>Aa Bb Gg Δδ Γγ Σσ Бб Фф</span><br>
petitecaps: <span class=petitecaps>Aa Bb Gg Δδ Γγ Σσ Бб Фф</span><br>
allpetitecaps: <span class=allpetitecaps>Aa Bb Gg Δδ Γγ Σσ Бб Фф</span><br>
unicase: <span class=unicase>Aa Bb Gg Δδ Γγ Σσ Бб Фф</span><br>
</div>
</body>
</html>
