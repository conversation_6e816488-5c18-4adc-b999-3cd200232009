<!DOCTYPE>
<html>
    <head>
        <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
         <link rel="stylesheet" type="text/css" href="resources/text-scaling.css">
        <script src="resources/text-scaling.js"></script>
        <script src="../../../resources/js-test.js"></script>
    </head>
    <body>
        <section>
            <h1>Font Size Scaling (LTR, Latin)</h1>
            <p>
                Size of the text should scale smoothly.
                Reported width should be within 0.02px of that of the highlighted reference row.
            </p>
            <div id="test"></div>
        </section>
        <script>
            if (window.testRunner && testRunner.setTextSubpixelPositioning)
                testRunner.setTextSubpixelPositioning(true);

            var PANGRAM = 'Pack my box with five dozen liquor jugs.';
            var results = runTest(document.getElementById('test'), PANGRAM);
            
            if (results == PASS) {
                testPassed('Size of text scales smoothly and width scales with font size as expected.');
                
                // Hide text if test passes as the actual numbers are
                // different across platforms and would require platform
                // specific baselines.
                if (window.testRunner)
                    document.getElementById('test').style.display = 'none';
            } else {
                testFailed('Size of text does not scale smoothly, reported widths highlighted in red do not match reference row.');
            }
        </script>
    </body>
</html>
