<head>
    <script>
        function test()
        {
            addTest(2, 3);
            addTest(2, 4);
            addTest(2, 5);
            addTest(2, 6);
            addTest(3, 4);
            addTest(3, 5);
            addTest(3, 6);
            addTest(4, 5);
            addTest(4, 6);
            addTest(5, 6);
        }

        function addTest(i, j)
        {
            var iframe = document.createElement("iframe");
            document.body.appendChild(iframe);
            var doc = iframe.contentDocument;
            var pre = doc.createElement("pre");
            var text = doc.createTextNode("foo\n\nbar");
            pre.appendChild(text);
            pre.style.margin = "0";
            doc.body.appendChild(pre);
            var sel = doc.defaultView.getSelection();
            sel.setBaseAndExtent(text, i, text, j);
        }
    </script>
    <style>
        iframe { border: 1px solid blue; width: 60px; height: 70px; margin: 4px; }
    </style>
</head>
<body>
    <p>
        Test for <i><a href="http://bugs.webkit.org/show_bug.cgi?id=13153">http://bugs.webkit.org/show_bug.cgi?id=13153</a>
        REGRESSION: Visual highlighting of pre-populated blank line in textarea is broken</i>.
    </p>
    <p>
        Testing the highlighting of and near hard line breaks.
    </p>
    <script>
        test();
    </script>
    <p>
        Testing the highlighting of text split across boxes.
    </p>
    <iframe style="width: 128px;" src="data:text/html,
        <div id='target'>Lorem    ipsum dolor</div>
        <script>
            var text = document.getElementById('target').firstChild;
            getSelection().setBaseAndExtent(text, 13, text, 16);
        </script>
    ">
    </iframe>
</body>
