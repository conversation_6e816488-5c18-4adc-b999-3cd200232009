<!DOCTYPE html>
<html>
  <head>
    <meta charset="UTF-8">
    <title>Mac Ascent Adjustment for Courier, Helvetica, Times</title>
    <style type="text/css">
      body {
          font-size: 16px;
          line-height: 25px;
      }

      .MidpointBox {
          background: lightgray;
          display: -webkit-flex;
          display: -ms-flexbox;
          display: flex;
          -webkit-align-items: center;
          -ms-flex-align: center;
          align-items: center;
          position: relative;
          margin: 1em;
          padding: .5em 1em;
      }

      .MidpointBox:before {
          content: "";
      position: absolute;
      height: 1px;
      top: 50%;
      left: 0;
      right: 0;
      margin-top: -1px;
      background: red;
      z-index: 1;
      }

      .MidpointBox-icon {
          height: 1em;
          width: 1em;
          margin-right: .5em;
          background: black;
      }
    </style>
  </head>
  <body>
    The red line needs to cross the middle of the black square, as well as through half the ascent of the capital
    letters and digits, for example the middle horizontal bar of the F or the crossing point of the two diagonals in the
    letter X. See also <a href="http://crbug.com/452967">bug 452967</a>.
    <div class="MidpointBox">
      <span class="MidpointBox-icon"></span>
      <span class="MidpointBox-text"><span style="font-family: 'Helvetica Neue';">38FGKX</span></span>
    </div>
    <div class="MidpointBox">
      <span class="MidpointBox-icon"></span>
      <span class="MidpointBox-text"><span style="font-family: Helvetica;">38FGKX</span></span>
    </div>
    <div class="MidpointBox">
      <span class="MidpointBox-icon"></span>
      <span class="MidpointBox-text"><span style="font-family: Consolas;">38FGKX</span></span>
    </div>
    <div class="MidpointBox">
      <span class="MidpointBox-icon"></span>
      <span class="MidpointBox-text"><span style="font-family: Courier;">38FGKX</span></span>
    </div>
    <div class="MidpointBox">
      <span class="MidpointBox-icon"></span>
      <span class="MidpointBox-text"><span style="font-family: Times;">38FGKX</span></span>
    </div>
    <div class="MidpointBox">
      <span class="MidpointBox-icon"></span>
      <span class="MidpointBox-text"><span style="font-family: Arial;">38FGKX</span></span>
    </div>
  </body>
</html>

