<!DOCTYPE html>
<html>
<head>
<style>
div {
  word-spacing: 1px;
}
.float {
  float: left;
}
.inline-block {
  display: inline-block;
}
</style>
</head>
<body>
<p>This test ensures that preferred width and layout match for blocks with word-spacing between inlines.
All the text below should be on one line.</p>
<div class="float" style="background-color: red"><span>a</span>
  <span>b</span></div>
<div class="float" style="background-color: orange">c
  d</div>
<div class="float" style="background-color: yellow">e
  <span>f</span></div>
<div class="float" style="background-color: green"><span>g</span>
  h</div>
<div class="inline-block" style="background-color: blue"><span>i</span>
  <span>j</span>
</div><div class="inline-block" style="background-color: indigo">k
  l
</div><div class="inline-block" style="background-color: purple">m
  <span>n</span>
</div><div class="inline-block" style="background-color: violet"><span>o</span>
  p</div>
</body>