<!DOCTYPE HTML>
<style>
html {
    -webkit-writing-mode:vertical-rl;
    -webkit-font-feature-settings:"vert" off;
}
@font-face {
    font-family:cssot;
    src:url(../../third_party/adobe-fonts/CSSFWOrientationTest.otf);
}
dd {
    border:black solid thin;
    font-family:Ahem, cssot;
    white-space:pre;
}
</style>
<dl>
<dt>Ideographic, Kana, CJK symbols</dt>
<dd style="inline-size:9em;">&#x56FD; &#x56FD; &#x3042; &#xFF01; &#x56FD;<br>&#x56FD;&#xFEFF;WWWWWWWW</dd>
<dd style="inline-size:9em;">&#x56FD; &#x56FD; &#x3042; &#xFF01; &#x56FD;<br>WWWWWWWWW</dd>
<dd style="inline-size:9em;">&#x56FD; &#x56FD; &#x3042; &#xFF01; &#x56FD;<br>WWWWWWWWW</dd>
<dd style="inline-size:9em;">&#x56FD; &#x56FD; &#x3042; &#xFF01; W<br>WWWWWWWWW</dd>
<dd style="inline-size:9em;">&#x20B9F; &#x20B9F; &#x20B9F; &#x20B9F; &#x20B9F;<br>&#x20B9F;&#xFEFF;WWWWWWWW</dd>
<dt>Ideographic, Kana, CJK symbols with spans</dt>
<dd style="inline-size:9em;">&#x56FD; &#x56FD; &#x3042; &#xFF01; &#x56FD;<br>&#x56FD;&#xFEFF;WWWWWWWW</dd>
<dt>Ideographic, Latin, and spaces</dt>
<dd style="inline-size:8em;">WW &#x56FD; W &#x56FD;<br>&#x56FD;&#xFEFF;WWWWWWW</dd>
<dd style="inline-size:11em;">WW &#x56FD;   W  &#x56FD;<br>&#x56FD;&#xFEFF;WWWWWWWWWW</dd>
<dd style="inline-size:11em;">WW &#x56FD;   W  &#x56FD;<br>WWWWWWWWWWW</dd>
<dd style="inline-size:11em;">WW &#x56FD;   W  &#x56FD;<br>WWWWWWWWWWW</dd>
<dd style="inline-size:8em;">WW &#x56FD;   W<br>&#x56FD;&#xFEFF;WWWWWWW</dd>
<dd style="inline-size:11em;">W.  W.  W &#x56FD;<br>WWWWWWWWWWW</dd>
</dl>
<script>
if (window.testRunner) {
    testRunner.waitUntilDone();
    window.onload = function () {
        document.fonts.ready.then(function () { testRunner.notifyDone(); });
    };
}
</script>
