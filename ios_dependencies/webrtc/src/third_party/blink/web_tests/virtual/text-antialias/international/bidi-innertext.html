<html>
    <meta http-equiv="content-type" content="text/html; charset=utf-8">
    <script>
    function print(message)
    {
        var paragraph = document.createElement("li");
        paragraph.appendChild(document.createTextNode(message));
        document.getElementById("console").appendChild(paragraph);
    }
    function test() {
        divs = document.getElementsByTagName("div");
        for(i = 0; div = divs.item(i); i++) {
            if(div.innerHTML == div.innerText)
                div.innerHTML = "Testing (" + div.innerHTML + ")";
            else
                div.innerHTML = "Testing (" + div.innerHTML + ") Found (" + div.innerText + ") FAILED";
                
        }
        
        
    }
    </script>
    <style>
    div {
        color: blue;
    }
    </style>

    <body onload="test();">
        The following tests text iteration over RTL text embedded with LTR text.  TextIterators are used for find, spellcheck, and .innerText
        <hr>
    
        Embedded Numbers: <div>חודא3ןמ</div>
        Embedded LTR: <div>חודאhelloןמ</div>
        Embedded Numbers and LTR<div>יקםל3קקרh</div>
        Embedded Numbers with spacing: <div>יקךךם34023חודאןמ 300 חודא</div>        
        Plain LTR: <div>hello</div>
        Embedded LTR with spacing: <div>חודיקךךם hello ןמי</div>
        Mixed but not embedded: <div>helloחודןמ</div>
        Mixed but not embedded: <div>חודןמhello</div>

        <ol id="console"></ol>
        
    </body>
</html>
