<!DOCTYPE html>
<html>
  <head>
    <title>find forwards and backwards</title>
    <script src="../../../resources/testharness.js"></script>
    <script src="../../../resources/testharnessreport.js"></script>
  </head>
  <body>
    <p>
      Tests find going both forward and backwards in small and large documents.
    </p>
    <script>
      function findForwards(textNode, pattern) {
        var selection = window.getSelection();
        selection.removeAllRanges();

        var beforeTextNodeRange = document.createRange();
        beforeTextNodeRange.setStartBefore(textNode);
        beforeTextNodeRange.setEndBefore(textNode);
        selection.addRange(beforeTextNodeRange);

        assert_true(find(pattern, false), 'Match expected.');
        assert_equals(selection.rangeCount, 1, 'Exactly one match expected.');

        var resultRange = selection.getRangeAt(0);
        assert_equals(resultRange.startContainer, textNode);
        assert_equals(resultRange.endContainer, textNode);

        return resultRange.startOffset + ', ' + resultRange.endOffset;
      }

      function findBackwards(textNode, pattern) {
        var selection = window.getSelection();
        selection.removeAllRanges();

        var afterTextNodeRange = document.createRange();
        afterTextNodeRange.setStartAfter(textNode);
        afterTextNodeRange.setEndAfter(textNode);
        selection.addRange(afterTextNodeRange);

        assert_true(find(pattern, false, true), 'Match expected.');
        assert_equals(selection.rangeCount, 1, 'Exactly one match expected.');

        var resultRange = selection.getRangeAt(0);
        assert_equals(resultRange.startContainer, textNode);
        assert_equals(resultRange.endContainer, textNode);

        return resultRange.startOffset + ', ' + resultRange.endOffset;
      }

      var textNode = document.createTextNode('');
      document.body.appendChild(textNode);

      textNode.textContent = 'abc';
      test(function() {
        assert_equals(findForwards(textNode, 'a'), '0, 1');
        assert_equals(findForwards(textNode, 'b'), '1, 2');
        assert_equals(findForwards(textNode, 'c'), '2, 3');
      }, 'Find forwards for short document.');

      test(function() {
        assert_equals(findBackwards(textNode, 'a'), '0, 1');
        assert_equals(findBackwards(textNode, 'b'), '1, 2');
        assert_equals(findBackwards(textNode, 'c'), '2, 3');
      }, 'Find backwards for short document.');

      var tenThousandChars = Array(10001).join('0');
      textNode.textContent = tenThousandChars + 'abc' + tenThousandChars;

      test(function() {
        assert_equals(findForwards(textNode, 'a'), '10000, 10001');
        assert_equals(findForwards(textNode, 'b'), '10001, 10002');
        assert_equals(findForwards(textNode, 'c'), '10002, 10003');
      }, 'Find forwards for long document.');

      test(function() {
        assert_equals(findBackwards(textNode, 'a'), '10000, 10001');
        assert_equals(findBackwards(textNode, 'b'), '10001, 10002');
        assert_equals(findBackwards(textNode, 'c'), '10002, 10003');
      }, 'Find backwards for long document.');

      document.body.removeChild(textNode);
    </script>
  </body>
</html>
