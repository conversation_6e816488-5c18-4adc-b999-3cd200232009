<!DOCTYPE html>
<html>
<head>
<meta charset="utf-8">
<script src="../../resources/js-test.js"></script>
</head>
<body onload="runTest();">
    <script>
        description("Soft Hyphen Test");

        function runTest() {
            if (window.testRunner)
                testRunner.dumpAsText();

            // 56 is 4 * the font size (14). We're expecting the text to break up into 4 lines.
            // 4 lines + the padding gives a div height of 92 in webkit and FF, 88 in chrome.
            // So anything less than 56 is a FAIL.
            var doesItPass = Boolean(document.getElementById('text1').offsetHeight > 56) ;
            shouldBeTrue(String(doesItPass)) ;
        }
    </script>

    <div id="text1" style="width:150px; font-family:Ahem; font-size:14px; border:2px solid red">
        <p>anti&shy;dis&shy;est&shy;ab&shy;lish&shy;ment&shy;arian&shy;ism.</p>
    </div>

</body>
</html>
