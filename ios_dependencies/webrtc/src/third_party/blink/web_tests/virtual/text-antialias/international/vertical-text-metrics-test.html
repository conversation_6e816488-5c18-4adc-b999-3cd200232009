<!DOCTYPE html>
<html lang="ja">
<head>
<meta http-equiv="content-type" content="text/html; charset=utf-8">
<title>Vertical text metrics test</title>
<script>
function print(message)
{
    var paragraph = document.createElement("li");
    paragraph.appendChild(document.createTextNode(message));
    document.getElementById("console").appendChild(paragraph);
}
function test()
{
    if (window.testRunner)
        testRunner.dumpAsText();

    var pElems = document.getElementsByTagName("span");
    for ( i = 0 ; i < pElems.length ; ++i )
    {
        var elem = pElems[i];
        print("width=" + elem.offsetWidth);
    }
}
</script>
<style>
body {
    font-family: "HiraMinPro-W3";
    font-size: 16pt;
}
#horizontal_TB {
    -webkit-writing-mode: horizontal-tb;
}
#vertical_RL {
    -webkit-writing-mode: vertical-rl;
}
#vertical_LR {
    -webkit-writing-mode: vertical-lr;
}
#horizontal_TB_complex {
    -webkit-writing-mode: horizontal-tb;
    text-rendering: optimizelegibility;
}
#vertical_RL_complex {
    -webkit-writing-mode: vertical-rl;
    text-rendering: optimizelegibility;
}
#vertical_LR_complex {
    -webkit-writing-mode: vertical-lr;
    text-rendering: optimizelegibility;
}
</style>
</head>
<body onload="test()">
<p>Simple text path</p>
<span id="horizontal_TB">string「あ、変っ！」。</span><br>
<span id="vertical_RL">string「あ、変っ！」。</span><br>
<span id="vertical_LR">string「あ、変っ！」。</span><br>
<br>
<hr>
<p>Complex text path</p>
<span id="horizontal_TB_complex">string「あ、変っ！」。</span><br>
<span id="vertical_RL_complex">string「あ、変っ！」。</span><br>
<span id="vertical_LR_complex">string「あ、変っ！」。</span><br>
<br>
<hr>
<p><ol id=console></ol></p>
</body>
</html>
