<!DOCTYPE html>
<style>
div {
  font-size: 10px;
  border: 1px blue solid;
  width: 10ch;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.padding > div {
  padding-right: 4ch;
}
.inline-block {
  background: red;
  display: inline-block;
  width: 4ch;
  height: 1em;
}
.layer {
  transform: translateY(0px);
}
</style>
<body>
  <div>123456789012</div>
  <div>12345678
    <span class="inline-block"></span>
  </div>
  <div>12345678
    <span class="inline-block layer"></span>
  </div>
  <div>1234567890123
    <span class="inline-block layer"></span>
  </div>
  <div>12345678
    <span class="layer">0000</span>
  </div>
  <div>1234567890123
    <span class="layer">0000</span>
  </div>
  <section class="padding">
    <div>123456789012</div>
    <div>12345678
      <span class="inline-block"></span>
    </div>
    <div>12345678
      <span class="inline-block layer"></span>
    </div>
    <div>1234567890123
      <span class="inline-block layer"></span>
    </div>
    <div>12345678
      <span class="layer">0000</span>
    </div>
    <div>1234567890123
      <span class="layer">0000</span>
    </div>
  </section>
</body>
