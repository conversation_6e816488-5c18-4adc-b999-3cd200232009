<!DOCTYPE html>
<html>
    <head>
        <meta charset="utf-8">
        <title>Shape across element boundary test</title>
        <script src="../../../resources/js-test.js"></script>
        <style>
            section { padding-left: 1rem; }
            section > div {
                display: inline-block;
                font-size: 1.5rem;
                width: 5rem;
                vertical-align: middle;
            }
            section > span {
                display: inline-block;
                width: 25rem;
                vertical-align: middle;
            }
        </style>
    </head>
    <body>
        <h1>Shape across element boundary test</h1>
        <section>
            <div><span  id="expected">سلام</span></div>
            The word by itself.
        </section>
        <section>
            <div><span id="split">س<span id="red" style="color: red">ل</span>ام</span></div>
            <span>
                Adding a span around a character that forms a ligature.
                Should render the same as the above, except that half
                of the middle glyph should be red.
            </span>
        </section>
        <section>
            <div>سل‍ام</div>
            Same word with a ZWJ character to disallow the ligature.
        </section>
        <section>
            <div><span id="isolated" style="color: red;">ل</span></div>
            The letter in red, in isolation.
        </section>
        <script>
            function width(id)
            {
                var el = document.getElementById(id);
                return el.getBoundingClientRect().width;
            }

            if (Math.abs(width('split') - width('expected')) > 0.1) {
                testFailed('Width of isolated word should match width of' +
                    ' word with markup.');
            } else {
                testPassed('Width of isolated word does match width of' +
                    ' word with markup.');
            }
        
            if (Math.abs(width('red') - width('isolated')) < 0.1) {
                testFailed('Width of partial glyph in ligature should not' +
                    ' match width of isolated glyph.');
            } else {
                testPassed('Width of partial glyph in ligature does not' +
                    ' match width of isolated glyph.');
            }
        </script>
    </body>
<html>


