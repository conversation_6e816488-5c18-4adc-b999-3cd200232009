<!DOCTYPE html>
<html>
<head>
<style>
    body {
        font-family: SubpixelPositioning;
    }
    div {
        font-family: SubpixelPositioningAhem;
        border: solid thin blue;
    }
    ::-webkit-scrollbar {
      width: 0px;
      height: 0px;
    }
</style>
<script>
    if (window.testRunner && testRunner.setTextSubpixelPositioning)
        testRunner.setTextSubpixelPositioning(true);
    if (window.internals)
        internals.setPageScaleFactor(1.7);
</script>
</head>
<body>
    Tests if the bottom of the text is truncated when the page is scaled by a fractional factor.
    If success, the text in the "overflow: hidden" div (the test case) should be displayed
    the same as in a normal div (the ref html).
    'p' is the character in ahem font with only the descent part.
    <br><br>
    <div style="font-size: 13px">&nbsp;pppp&nbsp;</div>
    <div style="font-size: 14px">&nbsp;pppp&nbsp;</div>
    <div style="font-size: 15px">&nbsp;pppp&nbsp;</div>
    <div style="font-size: 16px">&nbsp;pppp&nbsp;</div>
    <div style="font-size: 17px">&nbsp;pppp&nbsp;</div>
    <div style="font-size: 18px">&nbsp;pppp&nbsp;</div>
</body>
</html>
