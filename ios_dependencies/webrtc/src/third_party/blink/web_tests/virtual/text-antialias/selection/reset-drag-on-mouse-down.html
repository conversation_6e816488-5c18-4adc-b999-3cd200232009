<script>
window.onload = function(evt) {
  if (!window.testRunner)
    return;

  testRunner.dumpAsText();

  if (-1 == document.location.href.indexOf("?second")) {
    firstLoad();
  } else {
    secondLoad();
  }
}

function firstLoad() {
  testRunner.waitUntilDone();

  var link_x = document.getElementById("link").offsetLeft;
  var link_y = document.getElementById("link").offsetTop;

  eventSender.mouseMoveTo(link_x, link_y);
  eventSender.mouseDown();
  // The mouse move event causes us to consider starting a drag.
  eventSender.mouseMoveTo(link_x, link_y + 1);
  // But we don't move far before mouseup so it counts as a click.
  eventSender.mouseUp();
}

function secondLoad() {
  var text_x = document.getElementById("text").offsetLeft;
  var text_y = document.getElementById("text").offsetTop;

  eventSender.mouseMoveTo(text_x, text_y);
  eventSender.mouseDown();

  // Start the selection
  eventSender.mouseMoveTo(text_x, text_y + 1);

  // Create the selection
  eventSender.mouseMoveTo(text_x + 100, text_y + 100);
  eventSender.mouseUp();

  var range = window.getSelection().getRangeAt(0);
  var result = range.toString().length > 0 ? "SUCCESS" : "FAILED";
  document.getElementById("result").innerHTML = result;

  testRunner.notifyDone();
}
</script>
<p>Test for <a href="http://bugs.webkit.org/show_bug.cgi?id=16479">bug
16479</a>: text selection does not always begin at mouse down point.  To run
test manually, click on test link and then try to select some text below.  The
selected region should start where the mouse down starts.</p>

<p><a id="link" href="reset-drag-on-mouse-down.html?second" title="title">test link</a></p>

<p id="text">
TEXT TEXT TEXT TEXT TEXT TEXT TEXT TEXT TEXT TEXT TEXT TEXT TEXT TEXT TEXT TEXT
TEXT TEXT TEXT TEXT TEXT TEXT TEXT TEXT TEXT TEXT TEXT TEXT TEXT TEXT TEXT TEXT
TEXT TEXT TEXT TEXT TEXT TEXT TEXT TEXT TEXT TEXT TEXT TEXT TEXT TEXT TEXT TEXT
TEXT TEXT TEXT TEXT TEXT TEXT TEXT TEXT TEXT TEXT TEXT TEXT TEXT TEXT TEXT TEXT
TEXT TEXT TEXT TEXT TEXT TEXT TEXT TEXT TEXT TEXT TEXT TEXT TEXT TEXT TEXT TEXT
TEXT TEXT TEXT TEXT TEXT TEXT TEXT TEXT TEXT TEXT TEXT TEXT TEXT TEXT TEXT TEXT
TEXT TEXT TEXT TEXT TEXT TEXT TEXT TEXT TEXT TEXT TEXT TEXT TEXT TEXT TEXT TEXT
TEXT TEXT TEXT TEXT TEXT TEXT TEXT TEXT TEXT TEXT TEXT TEXT TEXT TEXT TEXT TEXT
TEXT TEXT TEXT TEXT TEXT TEXT TEXT TEXT TEXT TEXT TEXT TEXT TEXT TEXT TEXT TEXT
TEXT TEXT TEXT TEXT TEXT TEXT TEXT TEXT TEXT TEXT TEXT TEXT TEXT TEXT TEXT TEXT
TEXT TEXT TEXT TEXT TEXT TEXT TEXT TEXT TEXT TEXT TEXT TEXT TEXT TEXT TEXT TEXT
TEXT TEXT TEXT TEXT TEXT TEXT TEXT TEXT TEXT TEXT TEXT TEXT TEXT TEXT TEXT TEXT
TEXT TEXT TEXT TEXT TEXT TEXT TEXT TEXT TEXT TEXT TEXT TEXT TEXT TEXT TEXT TEXT
TEXT TEXT TEXT TEXT TEXT TEXT TEXT TEXT TEXT TEXT TEXT TEXT TEXT TEXT TEXT TEXT
TEXT TEXT TEXT TEXT TEXT TEXT TEXT TEXT TEXT TEXT TEXT TEXT TEXT TEXT TEXT TEXT
TEXT TEXT TEXT TEXT TEXT TEXT TEXT TEXT TEXT TEXT TEXT TEXT TEXT TEXT TEXT TEXT
TEXT TEXT TEXT TEXT TEXT TEXT TEXT TEXT TEXT TEXT TEXT TEXT TEXT TEXT TEXT TEXT
TEXT TEXT TEXT TEXT TEXT TEXT TEXT TEXT TEXT TEXT TEXT TEXT TEXT TEXT TEXT TEXT
TEXT TEXT TEXT TEXT TEXT TEXT TEXT TEXT TEXT TEXT TEXT TEXT TEXT TEXT TEXT TEXT
TEXT TEXT TEXT TEXT TEXT TEXT TEXT TEXT TEXT TEXT TEXT TEXT TEXT TEXT TEXT TEXT
</p>

<p id="result"></p>
