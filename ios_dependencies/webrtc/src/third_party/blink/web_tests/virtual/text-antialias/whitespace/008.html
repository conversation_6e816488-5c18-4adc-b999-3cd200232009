<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.0//EN">
<html>
 <head>
  <title>white-space normal: zero width non-breaking space</title>
  <script src="../../../resources/ahem.js"></script>
  <style type="text/css">
   /* setup */
   .control { display: inline; font: 1em/1 Ahem, sans-serif; background: red; color: white; }
   * { white-space: normall; font: inherit; }
   div { display: block; margin: 1em; }
   span { display: inline; }

   /* test */
   div { font: 20px/1 Ahem; width: 4em; background: red; color: green; }
  </style>
 </head>
 <body>
  <div class="control">Ahem_font_required_for_this_test.</div>
  <p>There should be a green block below, no red.</p>

  <div>
   xxxx
   xxxx
  </div>

 </body>
</html>
