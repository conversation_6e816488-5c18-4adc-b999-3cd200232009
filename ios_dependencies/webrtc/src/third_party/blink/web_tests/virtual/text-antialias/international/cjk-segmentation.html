<!DOCTYPE html>
<script src="../../../resources/testharness.js"></script>
<script src="../../../resources/testharnessreport.js"></script>
<script src="../../../editing/assert_selection.js"></script>
<script>
//Test Chinese Segmentation.

function expand_word_test(input, expected, title) {
  selection_test(
      input,
      selection => selection.getRangeAt(0).expand('word'),
      expected, `${title}: ${input} => ${expected}`);
}

// Case A
const kA0 = '\u56FD\u52A1\u9662'; // 国务院
const kA1 = '\u5173\u4E8E'; // 关于
const kA2 = '\u300A'; // 《
const kA3 = '\u571F\u5730'; // 土地
const kA4 = '\u623F\u5C4B'; // 房屋
const kA5 = '\u7BA1\u7406'; // 管理
const kA6 = '\u6761\u4F8B'; // 条例
const kA7 = '\u300B'; // 》
expand_word_test(
    `|${kA0}${kA1}${kA2}${kA3}${kA4}${kA5}${kA6}${kA7}`,
    `^${kA0}|${kA1}${kA2}${kA3}${kA4}${kA5}${kA6}${kA7}`,
    'Case A-0');
expand_word_test(
    `\u56FD|\u52A1\u9662${kA1}${kA2}${kA3}${kA4}${kA5}${kA6}${kA7}`,
    `^${kA0}|${kA1}${kA2}${kA3}${kA4}${kA5}${kA6}${kA7}`,
    'Case A-1');
expand_word_test(
    `\u56FD\u52A1|\u9662${kA1}${kA2}${kA3}${kA4}${kA5}${kA6}${kA7}`,
    `^${kA0}|${kA1}${kA2}${kA3}${kA4}${kA5}${kA6}${kA7}`,
    'Case A-2');
expand_word_test(
    `${kA0}|${kA1}${kA2}${kA3}${kA4}${kA5}${kA6}${kA7}`,
    `${kA0}^${kA1}|${kA2}${kA3}${kA4}${kA5}${kA6}${kA7}`,
    'Case A-3');
expand_word_test(
    `${kA0}\u5173|\u4E8E${kA2}${kA3}${kA4}${kA5}${kA6}${kA7}`,
    `${kA0}^${kA1}|${kA2}${kA3}${kA4}${kA5}${kA6}${kA7}`,
    'Case A-4');
expand_word_test(
    `${kA0}${kA1}|${kA2}${kA3}${kA4}${kA5}${kA6}${kA7}`,
    `${kA0}${kA1}^${kA2}|${kA3}${kA4}${kA5}${kA6}${kA7}`,
    'Case A-5');
expand_word_test(
    `${kA0}${kA1}${kA2}|${kA3}${kA4}${kA5}${kA6}${kA7}`,
    `${kA0}${kA1}${kA2}^${kA3}|${kA4}${kA5}${kA6}${kA7}`,
    'Case A-6');
expand_word_test(
    `${kA0}${kA1}${kA2}\u571F|\u5730${kA4}${kA5}${kA6}${kA7}`,
    `${kA0}${kA1}${kA2}^${kA3}|${kA4}${kA5}${kA6}${kA7}`,
    'Case A-7');
expand_word_test(
    `${kA0}${kA1}${kA2}${kA3}|${kA4}${kA5}${kA6}${kA7}`,
    `${kA0}${kA1}${kA2}${kA3}^${kA4}|${kA5}${kA6}${kA7}`,
    'Case A-8');
expand_word_test(
    `${kA0}${kA1}${kA2}${kA3}\u623F|\u5C4B${kA5}${kA6}${kA7}`,
    `${kA0}${kA1}${kA2}${kA3}^${kA4}|${kA5}${kA6}${kA7}`,
    'Case A-9');
expand_word_test(
    `${kA0}${kA1}${kA2}${kA3}${kA4}|${kA5}${kA6}${kA7}`,
    `${kA0}${kA1}${kA2}${kA3}${kA4}^${kA5}|${kA6}${kA7}`,
    'Case A-10');
expand_word_test(
    `${kA0}${kA1}${kA2}${kA3}${kA4}\u7BA1|\u7406${kA6}${kA7}`,
    `${kA0}${kA1}${kA2}${kA3}${kA4}^${kA5}|${kA6}${kA7}`,
    'Case A-11');
expand_word_test(
    `${kA0}${kA1}${kA2}${kA3}${kA4}${kA5}|${kA6}${kA7}`,
    `${kA0}${kA1}${kA2}${kA3}${kA4}${kA5}^${kA6}|${kA7}`,
    'Case A-12');
expand_word_test(
    `${kA0}${kA1}${kA2}${kA3}${kA4}${kA5}\u6761|\u4F8B${kA7}`,
    `${kA0}${kA1}${kA2}${kA3}${kA4}${kA5}^${kA6}|${kA7}`,
    'Case A-13');
expand_word_test(
    `${kA0}${kA1}${kA2}${kA3}${kA4}${kA5}${kA6}|${kA7}`,
    `${kA0}${kA1}${kA2}${kA3}${kA4}${kA5}${kA6}^${kA7}|`,
    'Case A-14');

// Case B
const kB0 = '\u7269\u4EF7'; // 物价
const kB1 = '\u9884\u671F'; // 预期
const kB2 = '\u8C03\u63A7'; // 调控
const kB3 = '\u76EE\u6807'; // 目标
const kB4 = '\u57FA\u672C'; // 基本
const kB5 = '\u5B9E\u73B0'; // 实现
expand_word_test(
    `|${kB0}${kB1}${kB2}${kB3}${kB4}${kB5}`,
    `^${kB0}|${kB1}${kB2}${kB3}${kB4}${kB5}`,
    'Case B-0');
expand_word_test(
    `\u7269|\u4EF7${kB1}${kB2}${kB3}${kB4}${kB5}`,
    `^${kB0}|${kB1}${kB2}${kB3}${kB4}${kB5}`,
    'Case B-1');
expand_word_test(
    `${kB0}|${kB1}${kB2}${kB3}${kB4}${kB5}`,
    `${kB0}^${kB1}|${kB2}${kB3}${kB4}${kB5}`,
    'Case B-2');
expand_word_test(
    `${kB0}\u9884|\u671F${kB2}${kB3}${kB4}${kB5}`,
    `${kB0}^${kB1}|${kB2}${kB3}${kB4}${kB5}`,
    'Case B-3');
expand_word_test(
    `${kB0}${kB1}|${kB2}${kB3}${kB4}${kB5}`,
    `${kB0}${kB1}^${kB2}|${kB3}${kB4}${kB5}`,
    'Case B-4');
expand_word_test(
    `${kB0}${kB1}\u8C03|\u63A7${kB3}${kB4}${kB5}`,
    `${kB0}${kB1}^${kB2}|${kB3}${kB4}${kB5}`,
    'Case B-5');
expand_word_test(
    `${kB0}${kB1}${kB2}|${kB3}${kB4}${kB5}`,
    `${kB0}${kB1}${kB2}^${kB3}|${kB4}${kB5}`,
    'Case B-6');
expand_word_test(
    `${kB0}${kB1}${kB2}\u76EE|\u6807${kB4}${kB5}`,
    `${kB0}${kB1}${kB2}^${kB3}|${kB4}${kB5}`,
    'Case B-7');
expand_word_test(
    `${kB0}${kB1}${kB2}${kB3}|${kB4}${kB5}`,
    `${kB0}${kB1}${kB2}${kB3}^${kB4}|${kB5}`,
    'Case B-8');
expand_word_test(
    `${kB0}${kB1}${kB2}${kB3}\u57FA|\u672C${kB5}`,
    `${kB0}${kB1}${kB2}${kB3}^${kB4}|${kB5}`,
    'Case B-9');
expand_word_test(
    `${kB0}${kB1}${kB2}${kB3}${kB4}|${kB5}`,
    `${kB0}${kB1}${kB2}${kB3}${kB4}^${kB5}|`,
    'Case B-10');
expand_word_test(
    `${kB0}${kB1}${kB2}${kB3}${kB4}\u5B9E|\u73B0`,
    `${kB0}${kB1}${kB2}${kB3}${kB4}^${kB5}|`,
    'Case B-11');

// Case C
const kC0 = '\u4FC4\u7F57\u65AF'; // 俄罗斯
const kC1 = '\u603B\u7EDF'; // 总统
const kC2 = '\uFF1A'; // ：
const kC3 = '\u673A\u573A'; // 机场
const kC4 = '\u7206\u70B8'; // 爆炸
const kC5 = '\u662F'; // 是
const kC6 = '\u6050\u6016'; // 恐怖
const kC7 = '\u88AD\u51FB'; // 袭击
expand_word_test(
    `|${kC0}${kC1}${kC2}${kC3}${kC4}${kC5}${kC6}${kC7}`,
    `^${kC0}|${kC1}${kC2}${kC3}${kC4}${kC5}${kC6}${kC7}`,
    'Case C-0');
expand_word_test(
    `\u4FC4|\u7F57\u65AF${kC1}${kC2}${kC3}${kC4}${kC5}${kC6}${kC7}`,
    `^${kC0}|${kC1}${kC2}${kC3}${kC4}${kC5}${kC6}${kC7}`,
    'Case C-1');
expand_word_test(
    `\u4FC4\u7F57|\u65AF${kC1}${kC2}${kC3}${kC4}${kC5}${kC6}${kC7}`,
    `^${kC0}|${kC1}${kC2}${kC3}${kC4}${kC5}${kC6}${kC7}`,
    'Case C-2');
expand_word_test(
    `${kC0}|${kC1}${kC2}${kC3}${kC4}${kC5}${kC6}${kC7}`,
    `${kC0}^${kC1}|${kC2}${kC3}${kC4}${kC5}${kC6}${kC7}`,
    'Case C-3');
expand_word_test(
    `${kC0}\u603B|\u7EDF${kC2}${kC3}${kC4}${kC5}${kC6}${kC7}`,
    `${kC0}^${kC1}|${kC2}${kC3}${kC4}${kC5}${kC6}${kC7}`,
    'Case C-4');
expand_word_test(
    `${kC0}${kC1}|${kC2}${kC3}${kC4}${kC5}${kC6}${kC7}`,
    `${kC0}${kC1}^${kC2}|${kC3}${kC4}${kC5}${kC6}${kC7}`,
    'Case C-5');
expand_word_test(
    `${kC0}${kC1}${kC2}|${kC3}${kC4}${kC5}${kC6}${kC7}`,
    `${kC0}${kC1}${kC2}^${kC3}|${kC4}${kC5}${kC6}${kC7}`,
    'Case C-6');
expand_word_test(
    `${kC0}${kC1}${kC2}${kC3}|${kC4}${kC5}${kC6}${kC7}`,
    `${kC0}${kC1}${kC2}${kC3}^${kC4}|${kC5}${kC6}${kC7}`,
    'Case C-7');
expand_word_test(
    `${kC0}${kC1}${kC2}${kC3}\u7206|\u70B8${kC5}${kC6}${kC7}`,
    `${kC0}${kC1}${kC2}${kC3}^${kC4}|${kC5}${kC6}${kC7}`,
    'Case C-8');
expand_word_test(
    `${kC0}${kC1}${kC2}${kC3}${kC4}|${kC5}${kC6}${kC7}`,
    `${kC0}${kC1}${kC2}${kC3}${kC4}^${kC5}|${kC6}${kC7}`,
    'Case C-9');
expand_word_test(
    `${kC0}${kC1}${kC2}${kC3}${kC4}${kC5}|${kC6}${kC7}`,
    `${kC0}${kC1}${kC2}${kC3}${kC4}${kC5}^${kC6}|${kC7}`,
    'Case C-10');
expand_word_test(
    `${kC0}${kC1}${kC2}${kC3}${kC4}${kC5}\u6050|\u6016${kC7}`,
    `${kC0}${kC1}${kC2}${kC3}${kC4}${kC5}^${kC6}|${kC7}`,
    'Case C-11');
expand_word_test(
    `${kC0}${kC1}${kC2}${kC3}${kC4}${kC5}${kC6}|${kC7}`,
    `${kC0}${kC1}${kC2}${kC3}${kC4}${kC5}${kC6}^${kC7}|`,
    'Case C-12');
expand_word_test(
    `${kC0}${kC1}${kC2}${kC3}${kC4}${kC5}${kC6}\u88AD|\u51FB`,
    `${kC0}${kC1}${kC2}${kC3}${kC4}${kC5}${kC6}^${kC7}|`,
    'Case C-13');

// Case D
const kD0 = '\u6625\u8FD0'; // 春运
const kD1 = '\u5929'; // 天
const kD2 = '\uFF0C'; // ，
const kD3 = '\u5317\u4EAC'; // 北
const kD4 = '\u8FD0\u9001'; // 运送
const kD5 = '\u65C5\u5BA2'; // 旅客
const kD6 = '\u4E07'; // 万
expand_word_test(
    `|${kD0}5${kD1}${kD2}${kD3}${kD4}${kD5}146${kD6}`,
    `^${kD0}|5${kD1}${kD2}${kD3}${kD4}${kD5}146${kD6}`,
    'Case D-0');
expand_word_test(
    `\u6625|\u8FD05${kD1}${kD2}${kD3}${kD4}${kD5}146${kD6}`,
    `^${kD0}|5${kD1}${kD2}${kD3}${kD4}${kD5}146${kD6}`,
    'Case D-1');
expand_word_test(
    `${kD0}|5${kD1}${kD2}${kD3}${kD4}${kD5}146${kD6}`,
    `${kD0}^5|${kD1}${kD2}${kD3}${kD4}${kD5}146${kD6}`,
    'Case D-2');
expand_word_test(
    `${kD0}5|${kD1}${kD2}${kD3}${kD4}${kD5}146${kD6}`,
    `${kD0}5^${kD1}|${kD2}${kD3}${kD4}${kD5}146${kD6}`,
    'Case D-3');
expand_word_test(
    `${kD0}5${kD1}|${kD2}${kD3}${kD4}${kD5}146${kD6}`,
    `${kD0}5${kD1}^${kD2}|${kD3}${kD4}${kD5}146${kD6}`,
    'Case D-4');
expand_word_test(
    `${kD0}5${kD1}${kD2}|${kD3}${kD4}${kD5}146${kD6}`,
    `${kD0}5${kD1}${kD2}^${kD3}|${kD4}${kD5}146${kD6}`,
    'Case D-5');
expand_word_test(
    `${kD0}5${kD1}${kD2}${kD3}|${kD4}${kD5}146${kD6}`,
    `${kD0}5${kD1}${kD2}${kD3}^${kD4}|${kD5}146${kD6}`,
    'Case D-6');
expand_word_test(
    `${kD0}5${kD1}${kD2}${kD3}${kD4}|${kD5}146${kD6}`,
    `${kD0}5${kD1}${kD2}${kD3}${kD4}^${kD5}|146${kD6}`,
    'Case D-7');
expand_word_test(
    `${kD0}5${kD1}${kD2}${kD3}${kD4}${kD5}|146${kD6}`,
    `${kD0}5${kD1}${kD2}${kD3}${kD4}${kD5}^146|${kD6}`,
    'Case D-8');
expand_word_test(
    `${kD0}5${kD1}${kD2}${kD3}${kD4}${kD5}14|6${kD6}`,
    `${kD0}5${kD1}${kD2}${kD3}${kD4}${kD5}^146|${kD6}`,
    'Case D-9');
expand_word_test(
    `${kD0}5${kD1}${kD2}${kD3}${kD4}${kD5}146|${kD6}`,
    `${kD0}5${kD1}${kD2}${kD3}${kD4}${kD5}146^${kD6}|`,
    'Case D-10');
</script>
