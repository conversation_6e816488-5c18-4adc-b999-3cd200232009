<!DOCTYPE html>
<style>
#container {
    width: 40px;
    border: 1px solid;
}
.text {
    white-space: nowrap;
}
</style>
<p>crbug.com/597193: Trailing space occurring before a no-wrap span should not fool it into thinking it can break. There should be three separate lines with the word 'Text' below.</p>
<p id="container">Text <span id="s1" class="text"></span> Text</p>
<script>
var s1 = document.getElementById('s1');
s1.appendChild(document.createTextNode("T"));
s1.appendChild(document.createTextNode("e"));
s1.appendChild(document.createTextNode("x"));
s1.appendChild(document.createTextNode("t"));
if (window.testRunner)
    testRunner.dumpAsText();
</script>
