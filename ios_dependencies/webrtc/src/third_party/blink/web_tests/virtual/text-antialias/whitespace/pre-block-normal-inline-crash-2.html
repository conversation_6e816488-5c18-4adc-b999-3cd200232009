<style>
body {
  margin-right: 100%;
}
.normal {
  padding-left: 1px;
  white-space: normal;
  word-wrap: break-word;
}
.pre {
  white-space: pre;
}
</style>
<div id=id1 style="height:1000px"></div>
<table>
<div>
<caption id=id5></caption>
<div class="pre"><span id=id3 class="normal">
 This test passes if it does not CRASH.<span id=id4></span></span></div>
</div>
</table>
</body>
<script>
id3.appendChild(id5);
document.documentElement.offsetTop;
id1.parentElement.removeChild(id1);
document.documentElement.offsetTop;
id4.appendChild(id5);
// Tests a case where an inline element that sets white-space:normal nested
// in a block that sets white-space:pre works correctly when there is a
// line break at the start of the inline element.
if (window.testRunner)
    testRunner.dumpAsText();
</script>
