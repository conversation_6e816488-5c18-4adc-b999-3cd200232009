<!DOCTYPE html>
<title>COLRv1 test font visual rendering test</title>
<link
  rel="help"
  href="https://www.w3.org/TR/css-fonts-4/#font-face-src-parsing"
/>
<link rel="author" href="<EMAIL>" />
<meta
  name="assert"
  content="Checks that COLRv1 glyphs are displayed in color."
/>
<meta charset="utf8" />
<style>
  @font-face {
    font-family: colrv1_samples;
    src: url(resources/test_glyphs-glyf_colr_1_variable.ttf);
  }

  .colrv1 {
    font: 35px/1 colrv1_samples, monospace;
    font-variation-settings: "SWPS" 45, "ROTA" 45, "SKXA" 45, "TRXX" 1.5, "TLDX"
    200, "SKXA" 30, "SKCX" 100, "SCOX" 100, "SCSX" 1.2, "SWPS" 45, "SWPE" 45,
    "CLIO" -300, "APH1" -0.5, "APH2" -0.5, "COL1" 0.5, "COL3" -0.25;
  }

  .columns {
    column-count: 3;
    height: 80vh;
  }
</style>
<div style="margin-bottom: 15px">
  Test passes if only color glyphs are seen below. Basic sanity check
  for <b>variable</b> COLRv1 rendering, additional testing is done in the Skia
  GM test for COLRv1, see Skia's gm/colrv1.cpp for details.
</div>
<div class="columns">
  The following two rows must feature purple (first row) and green (second row)
  in the glyph gradients.
  <div class="colrv1 foreground" style="color: purple"></div>
  <div class="colrv1 foreground" style="color: green"></div>
  <div class="colrv1 remainder"></div>
</div>
<script src="resources/colrv1-test-glyphs.js"></script>
<script>
  document.querySelectorAll(".foreground").forEach((aNode) => {
    aNode.innerText =
      glyph_descriptions["foreground_color"]["codepoints"].join("\u200B");
  });
  var hidden = ["util_contours", "circle_contours"];
  var remainderEl = document.querySelector(".remainder");
  for (const [key, value] of Object.entries(glyph_descriptions)) {
    if (hidden.includes(key)) continue;
    remainderEl.innerText += value.codepoints.join("\u200B") + " ";
  }
</script>
