<!doctype html>
<meta http-equiv="content-type" content="text/html; charset=UTF-8">
<style>
    body {
        font-size: 24px;
        font-family: Ahem;
    }

    div {
        -webkit-text-emphasis: sesame;
    }
    
</style>
<div id="target">Lorem</div>

<script>
function test() {
    if (!window.testRunner)
        return;
    testRunner.dumpAsText();
    var target = document.getElementById("target");
    eventSender.mouseMoveTo(target.offsetLeft, target.offsetTop);
    eventSender.mouseDown();
    eventSender.leapForward(500);
    eventSender.mouseUp();
    target.innerText = "PASS";
}

test();
</script>
