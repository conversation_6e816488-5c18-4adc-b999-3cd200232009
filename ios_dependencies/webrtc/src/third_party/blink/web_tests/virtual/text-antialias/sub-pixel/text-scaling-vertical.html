<!DOCTYPE>
<html>
    <head>
        <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
        <link rel="stylesheet" type="text/css" href="resources/text-scaling.css">
        <script src="resources/text-scaling.js"></script>
        <script src="../../../resources/js-test.js"></script>
        <style>
            section > div {
                -webkit-writing-mode: vertical-rl;
                writing-mode: vertical-rl;
            }
            section > div > div.header {
                border-width: 0 0 0 1px;
            }
            section > div > div > div {
                width: auto;
                height: 12ex;
                padding: 0 0 1ex 0;
            }
        </style>
    </head>
    <body>
        <section>
            <h1>Font Size Scaling (vertical-rl, Latin)</h1>
            <p>
                Size of the text should scale smoothly.
                Reported height (logical width) should be within 0.02px
                of that of the highlighted reference row.
            </p>
            <div id="test"></div>
        </section>
        <script>
            if (window.testRunner && testRunner.setTextSubpixelPositioning)
                testRunner.setTextSubpixelPositioning(true);

            var PANGRAM = 'Flygande bäckasiner söka hwila på mjuka tuvor.';
            var results = runTest(document.getElementById('test'), PANGRAM, 'vertical');

            if (results == PASS) {
                testPassed('Size of text scales smoothly and logical width scales with font size as expected.');
                
                // Hide text if test passes as the actual numbers are
                // different across platforms and would require platform
                // specific baselines.
                if (window.testRunner)
                    document.getElementById('test').style.display = 'none';
            } else {
                testFailed('Size of text does not scale smoothly, reported logical widths highlighted in red do not match reference row.');
            }
        </script>
    </body>
</html>
