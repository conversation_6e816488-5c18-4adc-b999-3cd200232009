<style>
/* A font that contains glyps only for U+0020 and U+3042, and both are blank. */
@font-face {
  font-family: 'SpaceAndHiraganaA';
  src: url('resources/SpaceAndHiraganaA.ttf');
}
p.test1 {
  font-family: SpaceAndHiraganaA;
  text-rendering: optimizeLegibility;
}
p.test2 {
  /* Arial doesn't contain a glyph for U+3042, hence the next font is selected. */
  font-family: Arial, SpaceAndHiraganaA;
  text-rendering: optimizeLegibility;
}
</style>
<p>White blank should follow this line. No garbage should be rendered.</p>
<p class="test1">&#x3042; &#x3042;</p>
<p class="test2">&#x3042; &#x3042;</p>
