<!DOCTYPE html>
<head>
<style>
    div {
        -webkit-writing-mode: vertical-rl;
        font-size: 24px;
    }
    span {
        -webkit-text-combine: horizontal;
    }
</style>
</head>

<body>
<div id="div">
  <span>PASS: </span><span id="target">did</span><span> not crash.</span>
</div>

<script>
  if (window.testRunner) {
    testRunner.waitUntilDone();
  }

  setTimeout(function() {
    var textNode = document.getElementById('target').firstChild;
    textNode.splitText(0);

    var sel = document.getSelection();
    sel.selectAllChildren(document.getElementById('div'));

    if (window.testRunner)
      testRunner.notifyDone();
  }, 0);
</script>
</body>
