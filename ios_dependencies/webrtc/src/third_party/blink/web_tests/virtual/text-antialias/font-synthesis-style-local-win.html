<!DOCTYPE html>
<html lang="en">
<meta charset="utf-8"/>
<title>CSS Test: font-synthesis-style: none disables fake italic/oblique</title>
<link rel="help" href="https://www.w3.org/TR/css-fonts-4/#font-synthesis-style">
<meta name="assert" content=" If ‘style’ is not specified, user agents must not synthesize italic faces">
<!--Sylfaen is only available in Regular style in the Windows set of test fonts that are used as system fonts in Windows testing-->
<style>
    @font-face {
        font-family: "Sylfaen local";
        src: local(Sylfaen);
    }
    @supports not (font-synthesis-style: none) {
        .test {color: red;}
    }
    .test {
        font-family: "Sylfaen local";
        font-size: 3em;
    }
    .nosynth {
        font-style: italic;
        font-synthesis-style: none;
    }
</style>

<p>Test passes if the two lines below are identical (the second line is <em>not obliqued</em>), and there is no red.</p>
<section class="test">
    <p>текст-заполнитель</p>
    <p class="nosynth">текст-заполнитель</p>
</section>
