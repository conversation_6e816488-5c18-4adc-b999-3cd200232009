<html>
<head>
<title>tt font size bug?</title>
<style type="text/css">
html, body { 
font-family: Verdana; 
}
tt, span {
font-size: 0.8em;
}
</style>
</head>
<body>
<h3>tt's, font-family inheriting and font-size: a bug</h3>
<table cellspacing="10">
<tr>
<td colspan="2">
The css in the head of this file is like this:<br/>
<br/>
html, body { <br/>
&nbsp; &nbsp; font-family: Verdana; <br/>
}<br/>
tt, span {<br/>
&nbsp; &nbsp; font-size: 0.8em;<br/>
}<br/>
<br/>
The two columns below show that inheriting the font does not correctly inherit the font-size for a &lt;tt&gt; block.<br/>
</td>
</tr>
<tr>
<th>
&lt;tt&gt;
</th>
<th>
&lt;span&gt;
</th>
</tr>
<tr>
<td>
<tt style="font-family: Verdana;">
font-family: Verdana; font-size: 0.8em;
</tt>
</td>
<td>
<span style="font-family: Verdana;">
font-family: Verdana; font-size: 0.8em;
</span>
</td>
</tr>
<tr>
<td>
<tt style="font-family: inherit;">
font-family: inherit (Verdana); font-size: 0.8em;
</tt>
</td>
<td>
<span style="font-family: inherit;">
font-family: inherit; font-size: 0.8em;
</span>
</td>
</tr>
<tr>
<td>

<tt style="font-family: Verdana; font-size: 1.0em;">
font-family: Verdana; font-size: 1.0em;
</tt>
</td>
<td>
<span style="font-family: Verdana; font-size: 1.0em;">
font-family: Verdana; font-size: 1.0em;
</span>
</td>
</tr>
<tr>
<td>
<tt>
default font; font-size: 0.8em;
</tt>
</td>
<td>
<span style="font-family: Courier;">
font-family: courier; font-size: 0.8em;
</span>
</td>
</tr>
</body>

</html>
