<!DOCTYPE html>
<title>Font Variant Shorthand Parsing</title>
<script src="../../../resources/testharness.js"></script>
<script src="../../../resources/testharnessreport.js"></script>
<script>
    var shorthandWriteExpectations = [
        { writeFontVariant : "normal", readCaps : "normal", readLigatures : "normal", readNumeric : "normal" },
        { writeFontVariant : "none", readCaps : "normal", readLigatures : "none", readNumeric : "normal" },
        { writeFontVariant : "all-small-caps", readCaps : "all-small-caps", readLigatures : "normal", readNumeric : "normal" },
        { writeFontVariant : "historical-ligatures", readCaps : "normal", readLigatures : "historical-ligatures", readNumeric : "normal" },
        { writeFontVariant : "historical-ligatures discretionary-ligatures", readCaps : "normal",
          readLigatures : "discretionary-ligatures historical-ligatures", readNumeric : "normal" },
        { writeFontVariant : "historical-ligatures petite-caps", readCaps : "petite-caps", readLigatures : "historical-ligatures", readNumeric : "normal" },
        { writeFontVariant : "", readCaps : "normal", readLigatures : "normal", readNumeric : "normal" },
        { writeFontVariant : "lining-nums", readCaps : "normal", readLigatures : "normal", readNumeric : "lining-nums" },
        { writeFontVariant : "lining-nums diagonal-fractions", readCaps : "normal",
          readLigatures : "normal", readNumeric : "lining-nums diagonal-fractions" },
        { writeFontVariant : "lining-nums all-small-caps", readCaps : "all-small-caps",
          readLigatures : "normal", readNumeric : "lining-nums" },
        { writeFontVariant : "lining-nums discretionary-ligatures", readCaps : "normal",
          readLigatures : "discretionary-ligatures", readNumeric : "lining-nums" },
        { writeFontVariant : "lining-nums historical-ligatures ordinal all-small-caps",
          readCaps : "all-small-caps",
          readLigatures : "historical-ligatures",
          readNumeric : "lining-nums ordinal" },
        { writeFontVariant : "ordinal discretionary-ligatures all-petite-caps historical-ligatures diagonal-fractions",
          readCaps : "all-petite-caps",
          readLigatures : "discretionary-ligatures historical-ligatures",
          readNumeric : "diagonal-fractions ordinal"
        }
    ];

    var shorthandReadExpectations = [
        { writeCaps : "", writeLigatures: "", writeNumeric: "", readFontVariant: "normal" },
        { writeCaps : "normal", writeLigatures: "normal", writeNumeric: "normal", readFontVariant: "normal" },
        { writeCaps : "normal", writeLigatures: "none", writeNumeric: "normal", readFontVariant: "none" },
        { writeCaps : "all-small-caps", writeLigatures: "none", writeNumeric: "normal",
          readFontVariant: "" },
        { writeCaps : "all-small-caps", writeLigatures: "normal", writeNumeric: "normal", readFontVariant: "all-small-caps" },
        { writeCaps : "normal",
          writeLigatures: "discretionary-ligatures historical-ligatures",
          writeNumeric: "normal",
          readFontVariant: "discretionary-ligatures historical-ligatures" },
        { writeCaps : "petite-caps", writeLigatures: "discretionary-ligatures", writeNumeric: "normal",
          readFontVariant: "discretionary-ligatures petite-caps" },
        { writeCaps : "petite-caps", writeLigatures: "discretionary-ligatures", writeNumeric: "lining-nums diagonal-fractions",
          readFontVariant: "discretionary-ligatures petite-caps lining-nums diagonal-fractions" },
        { writeCaps : "", writeLigatures: "", writeNumeric: "lining-nums diagonal-fractions",
          readFontVariant: "lining-nums diagonal-fractions" },
    ];

    var writeInvalidExpectations = {
        "font-variant" : [ "historical-ligaturesTYPO",
                        "historical-ligaturesTYPO small-caps",
                        "discretionary-ligatures no-discretionary-ligatures",
                        "discretionary-ligatures small-caps no-historical-ligatures all-small-caps",
                        "discretionary-ligatures small-caps all-petite-caps",
                        "small-caps unicase"],
        "font-variant-caps" : [ "typotypotypo", "all-small-caps all-petite-caps" ],
        "font-variant-ligatures" : [ "discretionary-ligatures no-discretionary-ligatures", "typotypotypo", "none discretionary-ligatures" ],
        "font-variant-numeric" : [ "lining-nums normal", "lining-nums slashed-zero diagonal-fractions oldstyle-nums" ]
    };
</script>
<div id="testElement"></div>

<div id="log"></div>
<script>
    setup({ explicit_done: true });

  function writeFontVariantTests() {
      for (testParameters of shorthandWriteExpectations) {
          testElement.style.fontVariant = testParameters.writeFontVariant;
          test(function(){
              assert_equals(getComputedStyle(testElement).fontVariantCaps, testParameters.readCaps);
              assert_equals(getComputedStyle(testElement).fontVariantLigatures, testParameters.readLigatures);
              assert_equals(getComputedStyle(testElement).fontVariantNumeric, testParameters.readNumeric);
          }, "Sub properties set from longhand value: " + testParameters.writeFontVariant);
      }
  }

  function readFontVariantTests() {
      for (testParameters of shorthandReadExpectations) {
          testElement.style.fontVariantCaps = testParameters.writeCaps;
          testElement.style.fontVariantLigatures = testParameters.writeLigatures;
          testElement.style.fontVariantNumeric = testParameters.writeNumeric;
          test(function(){
              assert_equals(getComputedStyle(testElement).fontVariant, testParameters.readFontVariant);
          }, "Shorthand from written subproperties: " + getComputedStyle(testElement).fontVariant);
      }
  }

  function invalidWriteTests() {
      for (key in writeInvalidExpectations) {
          for (value of writeInvalidExpectations[key]) {
              test(function(){
                  assert_false(CSS.supports(key, value));
              }, "Value " + value + " invalid for property " + key + ".");
          }
      }
  }

  window.addEventListener("load", function() {
      writeFontVariantTests();
      readFontVariantTests();
      invalidWriteTests();
      done();
  });
</script>
