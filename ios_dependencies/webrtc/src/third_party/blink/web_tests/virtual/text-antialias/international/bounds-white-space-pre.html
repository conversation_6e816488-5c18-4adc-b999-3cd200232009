﻿<!DOCTYPE html>
<meta charset="utf-8">
<script src="../../../resources/testharness.js"></script>
<script src="../../../resources/testharnessreport.js"></script>
<style>
#container {
    font-size: 25px;
}
</style>
<div id="container">
    <div>شییش ی ی ی ی ی</div>
    <div style="white-space:pre">شییش ی ی ی ی ی</div>
</div>
<script>
runTests();
function runTests() {
    var expectedNode = container.firstElementChild.firstChild;
    var targetNode = container.lastElementChild.firstChild;
    for (var i = 0; i < expectedNode.length; i++) {
        test(function () {
            assert_equals(getX(targetNode, i), getX(expectedNode, i));
        }, "Index " + i);
    }

    if (window.testRunner)
        container.style.display = "none";
}

function getX(node, offset) {
    var range = document.createRange();
    range.setStart(node, offset);
    range.setEnd(node, offset + 1);
    var bounds = range.getBoundingClientRect();
    return bounds.left;
}
</script>
