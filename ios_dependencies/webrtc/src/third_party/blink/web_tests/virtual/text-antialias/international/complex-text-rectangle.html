<meta charset=utf-8>

<span id="a">A&#x20d5;A&#xFE20;A&#x20d5;A&#x20d5;A&#x20d5;A&#x20d5;A&#x20d5;&#x20d5;A&#x034b;</span>

This test passes if it doesn't crash or log a failure message to the console.

<script>
if (window.testRunner)
    testRunner.dumpAsText();

var textNode = document.getElementById('a').firstChild;
var length = textNode.textContent.length;
for (var start = 0; start < length; ++start) {
    for (var end = 0; end < length; ++end) {
        var range = document.createRange();
        range.setStart(textNode, start);
        range.setEnd(textNode, end);
        var rect = range.getBoundingClientRect();
        if (rect.width < 0 || rect.width > 200) {
            console.log('FAIL: rect.width is ' + rect.width);
            break;
        }
    }
}
</script>
