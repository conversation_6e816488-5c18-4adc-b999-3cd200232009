<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01//EN" "http://www.w3.org/TR/html4/strict.dtd">
<html>
 <head>
  <title>HTML 4.01/CSS 2.1 Test: white-space (for complex text)</title>
  <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
  <link rel="help" href="http://www.w3.org/TR/CSS21/text.html#white-space-prop" title="16.6 Whitespace: the 'white-space' property">
 </head>
 <body>
  <p>This HTML tests if the 'while-space' property controls the behavors of line-break characters (U+000A and U+000D) in a complex text.</p>
  <p>If this test succeeds, you can see three words "&#x05E9;&#x05D5;&#x05BC;&#x05E8;&#x05D4;", separated with line-break.</p>
  <p style="white-space: pre;">&#x05E9;&#x05D5;&#x05BC;&#x05E8;&#x05D4;&#x000A;&#x05E9;&#x05D5;&#x05BC;&#x05E8;&#x05D4;&#x000D;&#x05E9;&#x05D5;&#x05BC;&#x05E8;&#x05D4;</p>
 </body>
</html>
