<!DOCTYPE html>
<p>There should be a space between every A and B.</p>
<div id="host1"> <span>B</span></div>
<div id="host2"><span style="position:absolute">A</span></div>
<div id="host3"><span slot="b">B</span> <span slot="a" style="position:absolute">A</span></div>
<div id="host4"><span style="display:contents"></span> <span>B</span></div>
<script>
  var root1 = host1.attachShadow({mode:"open"});
  root1.innerHTML = '<span style="position:absolute">A</span><slot></slot>';
  host1.offsetTop; // Force layout tree generation.
  root1.querySelector("span").style.position = "static";

  var root2 = host2.attachShadow({mode:"open"});
  root2.innerHTML = "<slot></slot> <span>B</span>";
  host2.offsetTop; // Force layout tree generation.
  host2.querySelector("span").style.position = "static";

  var root3 = host3.attachShadow({mode:"open"});
  root3.innerHTML = '<slot name="a"></slot><slot></slot><slot name="b"></slot>';
  host3.offsetTop; // Force layout tree generation.
  host3.querySelector("[slot=a]").style.position = "static";

  var root4 = host4.attachShadow({mode:"open"});
  root4.innerHTML = '<div style="unicode-bidi: normal" id="a">A</div><slot></slot>';
  host4.offsetTop; // Force layout tree generation.
  root4.querySelector("#a").style.display = "inline";
</script>
