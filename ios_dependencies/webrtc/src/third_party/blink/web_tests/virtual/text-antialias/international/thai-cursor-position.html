<html>
<head>
<title></title>
</head>
<body>
<p>This tests that the pixel-level x coordinate of a character range (0,0) doesn't change before and after inserting a Thai character. If this test finishes successfully, this test displays "SUCCEEDED". Otherwise this test displays "FAILED".</p>
<div id="test" contenteditable></div>
<ul id="console"></ul>
<script type="text/javascript">
function log(str) {
    var li = document.createElement("li");
    li.appendChild(document.createTextNode(str));
    var console = document.getElementById("console");
    console.appendChild(li);
}

if (window.testRunner) {
    testRunner.dumpAsText();
    var test = document.getElementById("test");
    test.focus();

    // Retrieve the pixel-level x coordinate of a character range (0,0) BEFORE inserting a Thai character.
    var rect0 = textInputController.firstRectForCharacterRange(0, 0);
    var x0 = rect0.toString().split(',')[0];

    // Insert a Thai character U+0E01.
    test.innerText += String.fromCharCode(0x0E01);

    // Retrieve the pixel-level x coordinate of a character range (0,0) AFTER inserting a Thai character.
    var rect1 = textInputController.firstRectForCharacterRange(0, 0);
    var x1 = rect1.toString().split(',')[0];

    // Compare the x coordinates. (They must be the same.)
    if (x0 == x1)
        log('SUCCEEDED');
    else
        log('FAILED: before=' + x0 + ', after=' + x1);
}
</script>
</body>
