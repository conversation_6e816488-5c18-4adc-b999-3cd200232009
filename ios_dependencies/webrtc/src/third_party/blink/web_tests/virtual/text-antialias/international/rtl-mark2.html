<html>
<body>
<p id="content">
&#x202e;&#xa60;&#x4e0c;&#x6103;&#x6201;&#x701b;&#x408c;&#x660;&#x1b01;&#x3804;
&#xc580;&#x2c03;&#xb1e6;&#x8034;&#xa009;&#xe100;&#x8653;&#x268e;&#x3801;&#xce48;
&#x339;&#x4003;&#xb220;&#x241;&#x9c80;&#x600;&#xacc2;&#x4005;&#xf00b;&#xa928;
</p>
<script>
if (window.testRunner)
    testRunner.dumpAsText();

// Add the PASS node, which will force a layout (which hangs without the fix).
document.body.appendChild(document.createTextNode("PASS: does not hang"));
// Remove the actual BiDi content, to keep the expectations file clean.
document.getElementById("content").innerHTML = "";
</script>
</body>
</html>
