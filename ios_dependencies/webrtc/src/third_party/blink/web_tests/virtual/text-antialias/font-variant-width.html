﻿<!DOCTYPE html>
<html>
<head>
<meta http-equiv="Content-Type" content="text/html;charset=utf-8" >
<title>Font Width Variant Text Combine</title>
<style>
@font-face {
    font-family: LiberationSansWidthVariants;
    src: url(../../third_party/LiberationSansWidthVariants/liberation_sans_width_variants.ttf) format("truetype");
}

#test {
    font-family: "LiberationSansWidthVariants";
    font-size: 24px;
    text-rendering: geometricPrecision;
    line-height: 100%;
    margin: 0;
    padding: 0px;
}

.hwid {
    -webkit-font-feature-settings: 'hwid';
}
.twid {
    -webkit-font-feature-settings: 'twid';
}
.qwid {
    -webkit-font-feature-settings: 'qwid';
}

</style>
    <script src="../../resources/testharness.js"></script>
    <script src="../../resources/testharnessreport.js"></script>
    <script>
    setup({ explicit_done: true });
    function testWidthVariant() {
        var elements = document.querySelectorAll("#test > div");
        for (var i = 0; i < elements.length; ++i) {
            var div = elements[i];
            var normal = div.firstChild;
            var text = normal.innerText;
            test(function () {
                var hwid = addVariant(normal, "hwid");
                var twid = addVariant(normal, "twid");
                var qwid = addVariant(normal, "qwid");
                var width = normal.getBoundingClientRect().width;
                var widthHwid = hwid.getBoundingClientRect().width;
                var widthTwid = twid.getBoundingClientRect().width;
                var widthQwid = qwid.getBoundingClientRect().width;
                assert_less_than(widthHwid, width, "hwid");
                assert_less_than(widthTwid, widthHwid, "twid");
                assert_less_than(widthQwid, widthTwid, "qwid");
            }, text);
        }
        done();
    }
    function addVariant(base, variantClassName) {
        var variant = base.cloneNode(true);
        variant.className = variantClassName;
        base.parentElement.appendChild(document.createTextNode(" "));
        base.parentElement.appendChild(variant);
        return variant;
    }
    </script>
</head>

<body onload="testWidthVariant();">
    <div id="test">
        <div><span>1</span></div>
        <div><span>9</span></div>
        <div><span>22</span></div>
        <div><span>99</span></div>
        <div><span>123456</span></div>
        <div><span>222222</span></div>
        <div><span>999999</span></div>
    </div>
    <div id="log"></div>
</body>
</html>

