<html>
<body>
<p id="content">&#x200F;&#x628;&#x62D;&#x631;&#x6CC;&#x646;&#xA;</p>
<script>
if (window.testRunner)
    testRunner.dumpAsText();

// Add the PASS node, which will force a layout (which hangs without the fix).
document.body.appendChild(document.createTextNode("PASS: does not hang"));
// Remove the actual BiDi content, to keep the expectations file clean.
document.getElementById("content").innerHTML = "";
</script>
</body>
</html>
