<p>
    Test offset for position computation across complex runs with font
    fallbacks, in this case due to a font (Arial) lacking a newline glyph.
</p>
<p id="result">
    Test did not run.
</p>
<div id="target" style="display: inline; font-family: Arial; text-rendering: optimizeLegibility;">
Jus&shy;ti&shy;fi&shy;ca&shy;tion
should
be used sparingly and cautiously on Web pages.
</div>
<script>
    if (window.testRunner) {
        testRunner.dumpAsText();

        var target = document.getElementById("target");
        var result = document.getElementById("result");
        var x = target.offsetLeft + target.offsetWidth / 2;
        var y = target.offsetTop + target.offsetHeight / 2;

        eventSender.leapForward(1000);
        eventSender.mouseMoveTo(x, y);
        eventSender.mouseDown();
        eventSender.mouseUp();
        eventSender.leapForward(1);
        eventSender.mouseDown();
        eventSender.mouseUp();
        eventSender.leapForward(1000);

        var selectedText = getSelection().toString();
        if (selectedText == "sparingly")
            result.innerText = "PASS";
        else
            result.innerText = "FAIL: Selected text is \"" + selectedText + "\"";
    }
</script>
