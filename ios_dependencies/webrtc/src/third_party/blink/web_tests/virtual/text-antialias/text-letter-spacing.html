<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN"
 "http://www.w3.org/TR/html4/loose.dtd">
<html>
<head>
<title>letter-spacing.html</title>
<style type="text/css">
body { overflow: hidden; }
p { 
    text-indent: 10em;
    background-color: #eee;
}
p.desc {
    text-indent: 0em;
    background-color: white;
}
.opaque {
    opacity: 0.5;
    background-color: #ccf;
}

.ls0 {
    letter-spacing: 0em;
}
.ls05 {
    letter-spacing: 0.5em;
}
.ls200 {
    letter-spacing: 200em;
}
.ls10000 {
    letter-spacing: 100000em;
}

.ls-05 { 
    letter-spacing: -0.5em;
}
.ls-200 {
    letter-spacing: -200em;
}
.ls-10000 { 
    letter-spacing: -10000em;
}

.rtl {
    direction: rtl;
    unicode-bidi: bidi-override;
}
     
</style>
</head>
<body>
<!-- these should render fine (tests regular, partially opaque text) -->
<p class="desc">The following lines test various combinations of
letter-spacing (especially negative values) and opacity. All of the lines
should say "Hello, world" but the spacing and background shading will vary
from test to test. The W3C specs are fairly silent on how user agents
should handle unusual values of letter-spacing, especially negative ones,
which are explicitly implementation-dependent. Different browsers will
render these differently, but hopefully the WebKit ports will be consistent,
at least.</p>

<p class="desc">The first line should be normally spaced.</p>
<p><span class="ls0">Hello, world</span></p>

<p class="desc">The next line tests a slight positive letter spacing, and
should be slightly spaced out (wider than normal).</p>
<p><span class="ls05">Hello, world</span></p>

<p class="desc">The next line tests a large positive letter spacing, and
should be *very* spaced out. You may only see an 'H' and a 'w', perhaps 
on two different lines, unless you scroll way to the right.</p>
<p><span class="ls200">Hello, world</span></p>

<p class="desc">The next line tests a very large positive letter spacing,
and should also be *very* spaced out. You may only see an 'H' and a 'w', 
perhaps on two different lines, unless you scroll way to the right.</p>
<p><span class="ls10000">Hello, world</span></p>

<p class="desc">The next line tests a small negative line spacing, and
should be crammed backwards</p>
<p><span class="ls-05">Hello, world</span></p>

<p class="desc">The next line tests a large negative line spacing, and
should be just display an 'H'</p>
<p><span class="ls-200">Hello, world</span></p>

<p class="desc">The next line tests a very large negative line spacing,
and should just have an 'H'.</p>
<p><span class="ls-10000">Hello, world</span></p>

<p class="desc">The next few lines test the interaction with opacity.
The first line should be normally spaced out, slightly opaque, 
and on a differently-colored background</p>
<p><span class="ls0 opaque">Hello, world</span></p>

<p class="desc">The next line tests a small positive line spacing, and 
should be slightly spaced out, opaque, and on a background.</p>
<p><span class="ls05 opaque">Hello, world</span></p>

<p class="desc">The next line tests a large positive line spacing, and
should be very widely spaced, on a background.</p>
<p><span class="ls200 opaque">Hello, world</span></p>

<p class="desc">The next line tests a very large positive line spacing, 
and should be very widely spaced but blank, and on a background.</p>
<p><span class="ls10000 opaque">Hello, world</span></p>

<p class="desc">The next line tests a small negative line spacing, and should
be blank.</p>
<p><span class="ls-05 opaque">Hello, world</span></p>

<p class="desc">The next line tests a large negative line spacing, and
should be blank.</p>
<p><span class="ls-200 opaque">Hello, world</span></p>

<p class="desc">The next line tests a very large negative line spacing,
and should be blank.</p>
<p><span class="ls-10000 opaque">Hello, world</span></p>

<p class="desc">Now we do a few basic right-to-left tests:</p>

<p class="desc">The first line should be normally spaced.</p>
<p><span class="ls0 rtl">Hello, world</span></p>

<p class="desc">The next line should be slightly spaced out (wider than
normal).</p>
<p><span class="ls05 rtl">Hello, world</span></p>
<p class="desc">The next line should be crammed backwards, just as if 
it wasn't right-to-left</p>
<p><span class="ls-05 rtl">Hello, world</span></p>

<p class="desc">The next line tests a large negative line spacing, and 
should just print a 'd'</p>
<p><span class="ls-200 rtl">Hello, world</span></p>

<p class="desc">Now, opacity as well - 
The next line should be printed right to left, slightly spaced out 
(wider than normal), and on a background.</p>
<p><span class="ls05 rtl opaque">Hello, world</span></p>

<p class="desc">The next line tests a small negative line spacing, and
should be blank.</p>
<p><span class="ls-05 rtl opaque">Hello, world</span></p>

<p class="desc">The next line tests a large negative line spacing, and
it should be blank.</p>
<p><span class="ls-200 rtl opaque">Hello, world</span></p>

</body>
</html>
