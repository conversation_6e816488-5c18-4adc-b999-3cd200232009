<!DOCTYPE html>
<meta charset='utf-8'>
<style>
div {
    width: 95px;
    font: 12px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}
.rtl {
  direction: rtl;
}
span {
    display: inline-block;
}
</style>
<p>crbug.com/133700: Ellipsis placed correctly in rtl text in an inline-block wth an rtl flow.</p>

<p>You should see text followed by ellipsis below.</p>
<div>
      <span>אבx</span> <span>דהx</span> <span>זחx</span> <span>אבx</span> <span>דהx</span> <span>זחx</span>
</div>

<p>You should see ellipsis followed by text below.</p>
<div class="rtl">
      <span>אבx</span> <span>דהx</span> <span>זחx</span> <span>אבx</span> <span>דהx</span> <span>זחx</span>
</div>

<p>You should see text followed by ellipsis below.</p>
<div>
      <span>אבגדהוזabcdefg<br>אבגדהוabcdefg</span> <span>זחa</span> <span>יכa</span>
</div>

<p>You should see ellipsis followed by text below.</p>
<div class="rtl">
      <span>אבגדהוזabcdefg<br>אבגדהוabcdefg</span> <span>זחa</span> <span>יכa</span>
</div>
