<p>
    This tests that <tt>-webkit-line-break: after-white-space</tt>
    trailing space truncation is not applied to text that does not auto-wrap.
</p>
<p>
    This box should have an active horizontal scrollbar, allowing the text to
    be scrolled to the left, making the trailing space on the right visible.
</p>
<div id="target" style="border: solid; width: 100px; white-space: nowrap; -webkit-nbsp-mode: space; -webkit-line-break: after-white-space; overflow-x: scroll;">
    Lorem ipsum dolor sit amet&nbsp;&nbsp;&nbsp;
</div>
<div id="reference" style="float: left;">
    Lorem ipsum dolor sit amet&nbsp;&nbsp;&nbsp;
</div>
<div id="result">
</div>
<script>
    if (window.testRunner)
        testRunner.dumpAsText();

    var target = document.getElementById("target");
    var reference = document.getElementById("reference");
    var result = document.getElementById("result");

    var referenceWidth = reference.offsetWidth;
    reference.style.display = "none";

    var scrollWidth = target.scrollWidth;

    result.innerText = Math.abs(scrollWidth - referenceWidth) < 1 ? "PASS" : "FAIL";
</script>
