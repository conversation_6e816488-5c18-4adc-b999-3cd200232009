<!DOCTYPE html>
<html>
    <head>
        <meta charset="utf-8">
        <title>Tests text range bounds</title>
        <style>
            div { text-rendering: optimizeLegibility; visibility: hidden; }
        </style>
        <script src="../../resources/testharness.js"></script>
        <script src="../../resources/testharnessreport.js"></script>
    </head>
    <body>
        <div id="test">Tests caret position reporting using ranges.</div>
        <script>
            var el = document.getElementById('test');
            var len = el.firstChild.textContent.length;

            function posForOffset(offset) {
                var r = document.createRange();
                r.setStart(el.firstChild, offset);
                r.setEnd(el.firstChild, offset);
                return r.getClientRects()[0].left;
            }

            test(function() {
                assert_true(posForOffset(len) > posForOffset(len - 1),
                    'Last caret position should be higher than second to last.');
                assert_true(posForOffset(len) != posForOffset(0),
                    'Last caret position should not match first.');
                }, 'Caret positions reported correctly.');
        </script>
    </body>
</html>