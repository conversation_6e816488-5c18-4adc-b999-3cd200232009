<!DOCTYPE html>
<style>
#container {
  font:50px/1 Ahem;
  width:3em;
}
#result {
  font:15px/1 monospace;
  white-space:pre;
}
</style>
<div id="result"></div>
<div id="container"></div>
<script src="resources/line-break-test.js"></script>
<script>
let matrix = new LineBreakTest(33, 127);
result.textContent = matrix.toResultString();
container.remove();

if (window.testRunner)
  testRunner.dumpAsText();
</script>
