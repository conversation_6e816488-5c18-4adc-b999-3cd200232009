<!DOCTYPE html>
<script src="../../../resources/testharness.js"></script>
<script src="../../../resources/testharnessreport.js"></script>

<script>
setup({single_test: true});

var aoScriptElements = document.getElementsByTagName("script");
for(var i = 0; i < aoScriptElements.length; i++) {
  aoScriptElements[i].parentNode.removeChild(aoScriptElements[i]);
}

var DOMNodeRemoved_active = false;
var DOMNodeRemoved_fired_count = 0;
function DOMNodeRemoved() {
  if (DOMNodeRemoved_active) return ;
  DOMNodeRemoved_active = true;

  document.execCommand('InsertOrderedList',false,false);

  var oSelection=window.getSelection();
  oSelection.modify('extend', 'forward', 'sentenceboundary');

  var oRange = oSelection.getRangeAt(0);

  var aoElements = document.getElementsByTagName("*");
  var oParentElement = aoElements[53 % aoElements.length];
  try { oRange.surroundContents(oParentElement) } catch(e) {}


  document.execCommand('InsertText',false,'\\\\\\\\\\\\m{VVVVVVVVVVVVVVV<};<WWWWW');
  DOMNodeRemoved_active = false;
}
document.addEventListener("DOMNodeRemoved", DOMNodeRemoved, true);

window.onload = function() {
  document.designMode = "on";

  var oSelection=window.getSelection();
  document.execCommand("SelectAll", false, false)

  var oRange = oSelection.getRangeAt(0);
  var oInsertedElement = document.getElementById('textarea');
  oRange.insertNode(oInsertedElement);

  var oRange = oSelection.getRangeAt(0);
  var oParentElement = document.getElementById('br');
  try { oRange.surroundContents(oParentElement) } catch (e) {}

  var oRange = oSelection.getRangeAt(0);
  var oInsertedElement = document.createElementNS('http://www.w3.org/2000/svg', 'view');
  oRange.insertNode(oInsertedElement);

  var oSelection = window.getSelection();
  document.execCommand("SelectAll", false, false)
  oSelection.collapseToStart();

  document.execCommand('Outdent',false,false);
  document.execCommand('Undo',false,false);

  var aoElements = document.getElementsByTagName("*");
  var oElement = aoElements[30];
  try { oElement.outerHTML = ""; } catch (e) {}

  var oRange = oSelection.getRangeAt(0);
  var oParentElement =  aoElements[24];
  try { oRange.surroundContents(oParentElement); } catch (e) {}
  done();
};
</script>

<style>
* {
  -webkit-appearance: media-volume-slider-container;
  direction: rtl;
}
.CLASS3 { font-style: oblique; }
.CLASS11 { display: inherit; }
*:first-letter { color: green; }
</style>

<head>
  <meta />
</head>

<body class="CLASS3">
<div id="log"></div>
<div>
<br id='br'/>
<button>
<table>
<caption></caption>
<col/>
</table>
</button>
<q class="CLASS11">
<textarea class="CLASS11"></textarea>
</option>
</optgroup>
</select>
<svg>
  <filter></filter>
  <radialGradient>
    <title></title>
  </radialGradient>
</svg>
<em></em>
<dfn></dfn>
</div>
<h3>
<textarea id="textarea"></textarea>
<svg>
<symbol>
<mask></mask>
<use>
<animateTransform>
<metadata>
</symbol>
<g>
<g>
<pattern></pattern>
</g>
</g>
</svg>
Text
<svg class="CLASS11">
<cursor></cursor>
<g></g>
<linearGradient></linearGradient>
<polygon</polygon>
<foreignObject></foreignObject>
<polyline></polyline>
Test passes if it does not CRASH.
