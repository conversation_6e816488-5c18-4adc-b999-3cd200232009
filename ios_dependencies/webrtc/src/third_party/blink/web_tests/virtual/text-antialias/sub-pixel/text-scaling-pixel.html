<!DOCTYPE>
<html>
    <head>
        <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
        <link rel="stylesheet" type="text/css" href="resources/text-scaling.css">
        <script src="resources/text-scaling.js"></script>
        <style>
            body { overflow: hidden; }
        </style>
    </head>
    <body style="overflow: hidden;">
        <section>
            <h1>Font Size Scaling Pixel Test</h1>
            <p>
                Size of the text should scale smoothly.
                Reported width should be within 0.02px of that of the highlighted reference row.
            </p>
            <div id="test"></div>
        </section>
        <script>
            if (window.testRunner && testRunner.setTextSubpixelPositioning)
                testRunner.setTextSubpixelPositioning(true);

            var PANGRAM = 'Amazingly few discotheques provide jukeboxes.';
            runTest(document.getElementById('test'), PANGRAM);
        </script>
    </body>
</html>
