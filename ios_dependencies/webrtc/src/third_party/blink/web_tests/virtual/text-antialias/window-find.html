<html>
<head>
<script>
if (window.testRunner) {
  testRunner.dumpAsText();
  testRunner.waitUntilDone();
}

function fail(s) {
  document.body.innerHTML = "FAIL: " + s;
}

function runTest() {
  // Need to force layout for window.find() to operate correctly.
  var forceLayout = document.body.offsetHeight;
  if (window.find('nonsense')) fail('found: nonsense');
  // https://bugs.webkit.org/show_bug.cgi?id=53654 -- failure when flipping
  // case sensitivity back-to-back.
  if (window.find('nonsense', true)) fail('found: nonsense');
  if (window.find('nonsense', false)) fail('found: nonsense');
  if (!window.find('for')) fail('not found: for');
  if (window.find('for')) fail('found: for');
  // Go backwards.
  if (!window.find('test', true, true, false)) fail('not found: test');
  if (window.find('for', true, true, false)) fail('found: for');
  // Backwards and case sensitivity.
  if (window.find('this', true, true, false)) fail('found: this');
  if (!window.find('This', true, true, false)) fail('not found: This');
  // Wrap-around forwards.
  if (!window.find('for', true, false, true)) fail('not found: for');
  if (!window.find('for', true, false, true)) fail('not found: for');
  // Wrap-around backwards.
  if (!window.find('for', true, true, true)) fail('not found: for');
  if (!window.find('for', true, true, true)) fail('not found: for');
  // Case sensitivity, forwards.
  if (!window.find('for', true, false, true)) fail('not found: for');
  if (!window.find('fOR', false, false, true)) fail('not found: for');
  if (!window.find('for', false, false, true)) fail('not found: for');
  if (!window.find('for', true, false, true)) fail('not found: for');
  if (window.find('FOR', true, false, true)) fail('found: FOR');
  // WholeWord.
  if (!window.find('for', false, false, true, true)) fail('whole word not found: for');
  if (!window.find('find', false, false, true, true)) fail('whole word not found: find');
  if (window.find('this', true, false, false, true)) fail('whole word found: this');
  if (window.find('est', false, false, false, true)) fail('whole word found: est');
  if (window.find('succ', false, false, false, true)) fail('whole word found: succ');

  if (window.testRunner)
    testRunner.notifyDone();
}
</script>
</head>
<body onload="runTest()">
This is a test for window.find(). SUCCESS!
</body>
</html>
