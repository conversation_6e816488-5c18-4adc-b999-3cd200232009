<html>
<head>
    <script>
        function canFind(target, specimen)
        {
            getSelection().empty();
            document.body.innerHTML = specimen;
            document.execCommand("FindString", false, target);
            var result = getSelection().rangeCount != 0;
            getSelection().empty();
            return result;
        }

        function runTests()
        {
            if (window.testRunner)
                testRunner.dumpAsText();

            var smallSharpS = String.fromCharCode(0xDF);

            var success = true;

            var message = "FAILURE:";

            if (!canFind("Stra" + smallSharpS + "e", "Stra" + smallSharpS + "e")) {
                success = false;
                message += " Cannot find small sharp s when searching for small sharp s.";
            }

            // Added an "x" to the start of the target string to work around an ICU bug.
            // See <http://bugs.icu-project.org/trac/ticket/6671> for details.
            if (!canFind("Strasse", "xStra" + smallSharpS + "e")) {
                success = false;
                message += " Cannot find small sharp s when searching for ss.";
            }

            if (!canFind("Stra" + smallSharpS + "e", "Strasse")) {
                success = false;
                message += " Cannot find ss when searching for small sharp s.";
            }

            if (!canFind("Strasse", "Strasse")) {
                success = false;
                message += " Cannot find ss when searching for ss. ";
            }

            if (success)
                message = "SUCCESS: Found all the variants of Strasse.";

            document.body.innerHTML = message;
        }
    </script>
</head>
<body onload="runTests()"></body>
</html>
