<html>
<head>
    <script>
        function canFind(target, specimen)
        {
            getSelection().empty();
            document.body.innerHTML = specimen;
            document.execCommand("FindString", false, target);
            var result = getSelection().rangeCount != 0;
            getSelection().empty();
            return result;
        }

        function runTests()
        {
            if (window.testRunner)
                testRunner.dumpAsText();

            var success = true;

            var nbsp = String.fromCharCode(0xA0);

            var message = "FAILURE:";

            if (canFind("", "a b")) {
                success = false;
                message += " Was able to find the empty string.";
            }

            if (!canFind(" ", "a b")) {
                success = false;
                message += " Cannot find space.";
            }

            if (canFind("  ", "a  b")) {
                success = false;
                message += " Two spaces are matching even though they should be collapsed.";
            }

            if (!canFind("  ", "<pre>a  b</pre>")) {
                success = false;
                message += " Cannot find two spaces in a &lt;pre&gt; element.";
            }

            if (!canFind(" ", "a&nbsp;b")) {
                success = false;
                message += " Cannot find an &amp;nbsp; when searching for a space.";
            }

            if (!canFind("  ", "a &nbsp;b")) {
                success = false;
                message += " Cannot find a space followed by &amp;nbsp; when searching for two spaces.";
            }

            if (!canFind("  ", "a&nbsp; b")) {
                success = false;
                message += " Cannot find an &amp;nbsp; followed by a space when searching for two spaces.";
            }

            if (!canFind(nbsp, "a b")) {
                success = false;
                message += " Cannot find space.";
            }

            if (canFind(nbsp + nbsp, "a  b")) {
                success = false;
                message += " Two spaces are matching even though they should be collapsed.";
            }

            if (!canFind(nbsp + nbsp, "<pre>a  b</pre>")) {
                success = false;
                message += " Cannot find two spaces in a &lt;pre&gt; element.";
            }

            if (!canFind(nbsp, "a&nbsp;b")) {
                success = false;
                message += " Cannot find an &amp;nbsp; when searching for a space.";
            }

            if (!canFind(nbsp + nbsp, "a &nbsp;b")) {
                success = false;
                message += " Cannot find a space followed by &amp;nbsp; when searching for two spaces.";
            }

            if (!canFind(nbsp + nbsp, "a&nbsp; b")) {
                success = false;
                message += " Cannot find an &amp;nbsp; followed by a space when searching for two spaces.";
            }

            if (success)
                message = "SUCCESS: Found all the spaces as expected.";

            document.body.innerHTML = message;
        }
    </script>
</head>
<body onload="runTests()"></body>
</html>
