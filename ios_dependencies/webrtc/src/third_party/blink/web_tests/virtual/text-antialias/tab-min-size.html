<!DOCTYPE html>
<style>
span {
  font-family: monospace;
  tab-size: 2;
  white-space: pre;
  width: fit-content;
}
</style>
<div><span id="one-tab"><span>&Tab;</span>word</span></div>
<div><span id="two-tabs"><span>&Tab;</span><span>&Tab;</span>word</span></div>
<script src="../../resources/js-test.js"></script>
<script>
  description('Tab characters should never be zero-width.');

  var oneTabLineWidth = document.getElementById("one-tab").getBoundingClientRect().width;
  var twoTabLineWidth = document.getElementById("two-tabs").getBoundingClientRect().width;

  if (oneTabLineWidth == twoTabLineWidth) {
    testFailed("oneTabLineWidth should not be equal to twoTabLineWidth.");
  } else {
    testPassed("oneTabLineWidth is not equal to twoTabLineWidth.");
  }
</script>
