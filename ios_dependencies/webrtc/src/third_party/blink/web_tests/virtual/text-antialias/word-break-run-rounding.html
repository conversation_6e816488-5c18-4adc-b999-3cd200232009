<html>
<head>
    <style>
        div { border: solid; word-break:break-all; font-family:'Lucida Grande'; }
    </style>
</head>
<body>
<p>
    Tests for <i><a href="http://bugs.webkit.org/show_bug.cgi?id=13438">http://bugs.webkit.org/show_bug.cgi?id=13438</a>
    Run rounding makes word-break:break-all/word not functional</i>.
</p>
<p>
    Test that the preferred (maximum) width is right:
</p>
<div style="float: left;">
    The black border should fit tightly around this one line of text with no space after the period.
</div>
<br style="clear: both;">
<p>
    Test that text does not wrap too early due to rounding errors:
</p>
<div style="width: 425px;">
    This text fits nicely on a single line of the given width.
</div>

<p>
    Test that text does not wrap too late:
</p>
<!-- Note skipped whitespace before the long word -->
<div style="width: 591px;">
    J u s t a b u n c h o f l e t t e r s h e r e , n o t h i n g t o s e e .  Thisisonebigwordwhichisverylong.
</div>
</body>
</html>
