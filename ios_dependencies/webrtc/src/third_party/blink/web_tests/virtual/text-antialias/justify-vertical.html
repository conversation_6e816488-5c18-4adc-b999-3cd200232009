<!DOCTYPE HTML>
<style>
html {
    -webkit-writing-mode:vertical-rl;
    -webkit-font-feature-settings:"vert" off;
}
@font-face {
    font-family:cssot;
    src:url(../../third_party/adobe-fonts/CSSFWOrientationTest.otf);
}
dd {
    border:black solid thin;
    font-family:Ahem, cssot;
    text-align:justify;
    text-autospace: no-autospace;
}
</style>
<dl>
<dt>Ideographic, Kana, CJK symbols</dt>
<dd style="inline-size:9em;">&#x56FD;&#x56FD;&#x3042;&#xFF01;&#x56FD;&#x56FD;&#xFEFF;WWWWWWWW</dd>
<dd style="inline-size:9em;">&#x56FD;&#x56FD;&#x3042;&#xFF01;&#x56FD;WWWWWWWWW</dd>
<dd style="inline-size:9em;">&#x56FD;&#x56FD;&#x3042;&#xFF01;&#x56FD; WWWWWWWWW</dd>
<dd style="inline-size:9em;">&#x56FD;&#x56FD;&#x3042;&#xFF01;W WWWWWWWWW</dd>
<dd style="inline-size:9em;">&#x20B9F;&#x20B9F;&#x20B9F;&#x20B9F;&#x20B9F;&#x20B9F;&#xFEFF;WWWWWWWW</dd>
<dt>Ideographic, Kana, CJK symbols with spans</dt>
<dd style="inline-size:9em;">&#x56FD;<span>&#x56FD;</span>&#x3042;&#xFF01;&#x56FD;&#x56FD;&#xFEFF;WWWWWWWW</dd>
<dt>Ideographic, Latin, and spaces</dt>
<dd style="inline-size:8em;">WW&#x56FD;W&#x56FD;&#x56FD;&#xFEFF;WWWWWWW</dd>
<dd style="inline-size:11em;">WW&#x56FD; W &#x56FD;&#x56FD;&#xFEFF;WWWWWWWWWW</dd>
<dd style="inline-size:11em;">WW&#x56FD; W &#x56FD;WWWWWWWWWWW</dd>
<dd style="inline-size:11em;">WW&#x56FD; W &#x56FD; WWWWWWWWWWW</dd>
<dd style="inline-size:8em;">WW&#x56FD; W &#x56FD;&#xFEFF;WWWWWWW</dd>
<dd style="inline-size:11em;">W. W. <span>W</span>&#x56FD;WWWWWWWWWWW</dd>
<!-- These are not supported yet
<dt>Combining and IVS</dt>
<dd style="inline-size:5em;">&#x304B;&#x3099;&#x304B;&#x3099;&#x304B;&#x3099;&#x304B;&#x3099;&#xFEFF;WWWW</dd>
<dd style="inline-size:5em;">&#x8FBA;&#xE0102;&#x8FBA;&#xE0102;&#x8FBA;&#xE0102;&#x8FBA;&#xFEFF;WWWW</dd>
-->
</dl>
<script>
if (window.testRunner) {
    testRunner.waitUntilDone();
    window.onload = function () {
        document.fonts.ready.then(function () { testRunner.notifyDone(); });
    };
}
</script>
