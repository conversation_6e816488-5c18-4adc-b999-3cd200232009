<html>
<head>
    <title></title>
    <style type="text/css">
        div {
            background: yellow;
            width: 200px;
            font: 11px 'Lucida Grande';
            word-wrap: break-word;
        }
    </style>
</head>
<body>
    <p>
        Test for <i><a href="http://bugs.webkit.org/show_bug.cgi?id=12726">http://bugs.webkit.org/show_bug.cgi?id=12726</a>
        REGRESSION (r12073): Text wraps in the middle of a word instead of wrapping at the space before the word</i>.
    </p>
    <p>
        &ldquo;onelongwrodwithnobreaks&rdquo; should not break in the middle.
    </p>
    <div>lllllllllllllllllllllllllllllllllllllllllllllllllllllll onelongwrodwithnobreaks</div>
</body>
</html>
