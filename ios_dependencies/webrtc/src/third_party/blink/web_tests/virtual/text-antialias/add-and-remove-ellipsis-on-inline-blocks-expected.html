<!DOCTYPE html>
<style>
  #container {
    background: lightblue;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
  }
  .child {
    display: inline-block;
  }
</style>
crbug.com/762835: inline-block children aren't visible after collapsing/expanding container with `text-overflow: ellipsis`
<div id=container>
  <div class=child>child1</div>
  <div class=child>child2</div>
  <div class=child>child3</div>
  <div class=child>child4</div>
  <div class=child>child5</div>
  <div class=child>child6</div>
</div>
