<!DoCTYPE html>
<style>
.background {
    width: 210px;
    height: 210px;
    background-color: green;
}
.outer {
    white-space: nowrap;
    width: 100px;
    height: 200px;
}
.inner {
    width: 50px;
    height: 100px;
    display: inline-block;
    white-space: normal;
    background-color: green;
}
.float {
    float: left;
}
</style>
<p>crbug.com:552475: Floating elements that wrap their contents shouldn't cause a line-break in a no-wrap context. There should be a green square below.</p>
<div class="background">
    <div class="outer">
        <div class="inner"></div>
        <div class="inner"></div>
        <div class="float"></div>
        <div class="inner"></div>
    </div>
</div>
