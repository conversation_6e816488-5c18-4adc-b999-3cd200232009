<HTML>
<HEAD>
<TITLE>Safari 1.0 PRE width bug</TITLE>
<STYLE TYPE="text/css">
   <!--
   .box { background: #cc9; border: black solid 1px ; padding:0.5em; }
   .dow { border-top: solid black 1px; border-bottom: solid black 2px; }
   -->
</STYLE>
</HEAD>
<BODY BGCOLOR="#FFFFFF" TEXT="#000000">

<H1>Safari 1.0 PRE width bug</H1>

 As noted in my <A HREF="/1/blog/permalink/2003.0920">blog</A>,
 in certain cases Safari will miscalculate the width of a PRE element.
 I think I've found the trigger. If a nested container element is preceded by
 uncontained text and a blank line, and there are "too many" characters
 contained in the nested element, the PRE width will be too wide.
 In the examples below, the nested element is a SPAN whose content
 is the days-of-week line, as follows:
 <PRE>
    &lt;SPAN CLASS=dow&gt; D  M  T  W  J  F  S&lt;/SPAN&gt;
</PRE>

<H2>Example 1</H2>

<table valign=top width="100%"><tr><td width="1%">
<PRE CLASS=box>        2003

<SPAN CLASS=dow> D  M  T  W  J  F  S</SPAN>

      September
    1  2  3  4  5  6
 7  8  9 10 11 12 13
14 15 16 17 18 19 20
21 22 23 24 25 26 27
28 29 30</PRE>
</td>
<td valign=top width="99%">The blank line between '2003' and the nested element
 triggers the width bug.
</td>
</tr></table><BR CLEAR=all>

<H2>Example 2</H2>

<table valign=top width="100%"><tr><td width="1%">
<PRE CLASS=box>        2003

<SPAN CLASS=dow> D  M  TWJFS</SPAN>

      September
    1  2  3  4  5  6
 7  8  9 10 11 12 13
14 15 16 17 18 19 20
21 22 23 24 25 26 27
28 29 30</PRE>
</td>
<td valign=top width="99%">The blank line between '2003' and the nested element
 triggers the width bug.
 <P>Here, the excess whitespace is reduced by reducing the
 number of characters contained by the nested element.</P>
</td>
</tr></table><BR CLEAR=all>

<H2>Example 3</H2>

<table valign=top width="100%"><tr><td width="1%">
<PRE CLASS=box>        2003
<SPAN CLASS=dow> D  M  T  W  J  F  S</SPAN>

      September
    1  2  3  4  5  6
 7  8  9 10 11 12 13
14 15 16 17 18 19 20
21 22 23 24 25 26 27
28 29 30</PRE>
</td>
<td valign=top width="99%">The blank line between '2003' and the nested element
 has been removed, so the bug is not triggered.
</td>
</tr></table><BR CLEAR=all>

<H2>Example 4</H2>

<table valign=top width="100%"><tr><td width="1%">
<PRE CLASS=box><SPAN>        2003</SPAN>

<SPAN CLASS=dow> D  M  T  W  J  F  S</SPAN>

      September
    1  2  3  4  5  6
 7  8  9 10 11 12 13
14 15 16 17 18 19 20
21 22 23 24 25 26 27
28 29 30</PRE>
</td>
<td valign=top width="99%">The blank line between '2003' and the nested element
 no longer triggers the width bug, because now the '2003' is itself contained by
 a nested element (in this case, another SPAN).
</td>
</tr></table><BR CLEAR=all>


</BODY>
</HTML>
