<!DOCTYPE html>
<meta charset='utf-8'>
<style>
div {
    width: 125px;
    height: 20px;
    font: 12px;
    overflow: hidden;
    white-space: pre;
    text-overflow: ellipsis;
    direction: rtl;
}
</style>
<p>crbug.com/636060: rtl text in a rtl flow should truncate the text left-to-right and underline correctly in composition mode.</p>
<p>You should see an underline beneath the text.</p>
<div id="textarea" contenteditable="true"></div>
<script>
if (window.testRunner) {
  var textarea = document.getElementById("textarea");
  textarea.focus();
  textInputController.setComposition("אבגדהוזחטיכךלמםסעפףצץקרשת<");
}
</script>
