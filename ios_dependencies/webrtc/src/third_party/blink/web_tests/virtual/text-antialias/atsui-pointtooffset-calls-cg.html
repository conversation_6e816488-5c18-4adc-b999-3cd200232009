<html>
<head>
<title>pointToOffset always takes the CG code path</title>
<script type="text/javascript">
function step7()
{
    eventSender.mouseUp();
    testRunner.notifyDone();
}

function step4()
{
    eventSender.mouseDown();
    window.setTimeout(step7, 1);
}

function step3()
{
    eventSender.mouseMoveTo(39, 10);
    window.setTimeout(step4, 1000);
}

step3();
testRunner.waitUntilDone();
</script>
</head>
<body style="margin: 0; padding: 0;"">
<!-- The &#x0300;s force ATSUI rendering; when measured by
the CG code path, they take up extra width -->
e&#x0300;e&#x0300;e&#x0300;e&#x0300;e&#x0300;e&#x0300;X
<hr>
This tests for regressions against
<i>http://bugzilla.opendarwin.org/show_bug.cgi?id=5878 pointToOffset
always takes the CG code path</i> by clicking the X and verifying that the
correct caret position (13) is reported to the editing delegate.
</body>
</html>
