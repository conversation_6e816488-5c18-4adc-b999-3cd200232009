<html>
<head>
<title>Line breaks after hyphen before number</title>
<style>
.test { width: 1px; }
</style>
</head>
<body>
<script>
if (window.testRunner)
    testRunner.dumpAsText();

var tests = [
    "$-1111111",
    "-2222.2222",
    "-.33333333",
    "4444444-4444444",
    "5555555-abcdefg",
    "abcdefg-6666666"
];

// Each of the above tests should look like corresponding expectation below.
var expected = [
    "$-1111111",
    "-2222.2222",
    "-.33333333",
    "4444444-<br>4444444",
    "5555555-<br>abcdefg",
    "abcdefg-<br>6666666"
];

var allSuccess = true;
for (var i = 1; i <= tests.length; ++i) {
    document.write("Test " + i + ":");
    document.write("<div class='test' id='test" + i + "'>" + tests[i - 1] + "</div>");
    document.write("Expected:<div id='expected" + i + "'>" + expected[i - 1] + "</div>");
    var success = getComputedStyle(document.getElementById("test" + i, "")).height
         == getComputedStyle(document.getElementById("expected" + i, "")).height;
    document.write(success ? "PASS" : "FAIL");
    document.write("<br><br>");
    allSuccess = allSuccess && success;
}

document.write("Summary: " + (allSuccess ? "PASS" : "FAIL"));
</script>
</body>
</html>
