<script src="../../resources/ahem.js"></script>
<style>
    div {
        height: 72px;
        font-family: Ahem;
        font-size: 24px; 
    }
</style>
<p>
    Test that the intensity of text-shadow does not depend on the transparency
    of the text fill color.
</p>
<div style="text-shadow: green 0 36px 0;">
    A
    <span style="color: transparent;">A</span>
    <span style="color: rgba(0, 0, 0, 0.1);">A</span>
</div>
<p>
    There should be three identical green squares on the line above.
</p>
<div style="text-shadow: green 0 36px 3px;">
    A
    <span style="color: transparent;">A</span>
    <span style="color: rgba(0, 0, 0, 0.1);">A</span>
</div>
<p>
    There should be three identical soft-edged green squares on the line above.
</p>
<div style="text-shadow: green 0 36px 0; -webkit-text-stroke: 5px blue;">
    A
    <span style="color: transparent;">A</span>
    <span style="color: rgba(0, 0, 0, 0.1);">A</span>
</div>
<p>
    There should be three identical green squares on the line above.
</p>
