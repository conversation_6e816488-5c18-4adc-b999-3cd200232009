<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" 
"http://www.w3.org/TR/html4/loose.dtd">
<html>
<head>
<title>block-flow parser test</title>
<script>
function print(message)
{
    var paragraph = document.createElement("li");
    paragraph.appendChild(document.createTextNode(message));
    document.getElementById("console").appendChild(paragraph);
}
function test()
{
    if (window.testRunner)
        testRunner.dumpAsText();

	var blockFlowValues = ["tb", "rl", "lr", "bt"];
	var pElems = document.getElementsByTagName("div");
	for ( i = 0 ; i < pElems.length ; ++i )
	{
		var elem = pElems[i];
		elem.style.webkitBlockFlow = blockFlowValues[i];
		print("-webkit-block-flow:" + elem.style.webkitBlockFlow);
	}
}
</script>
</head>
<body onload="test()">
<p>block-flow parser test</p>
<div>-webkit-block-flow:tb</div>
<div>-webkit-block-flow:rl</div>
<div>-webkit-block-flow:lr</div>
<div>-webkit-block-flow:bt</div>
<p>Okay if -webkit-block-flow values above and below are the same.</p>
<hr>
<p><ol id=console></ol></p>
</body>
</html>
