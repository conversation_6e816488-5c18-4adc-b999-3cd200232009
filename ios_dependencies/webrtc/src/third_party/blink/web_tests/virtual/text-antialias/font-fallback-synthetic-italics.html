<!DOCTYPE html>
<html>
    <head>
        <meta charset="utf-8">
        <script src="../../resources/js-test.js"></script>
        <style>
            #test, #reference {
                font-style: italic;
                font-size: 32px;
            }
            #test {
                font-family: arial, serif;
            }
            #reference {
                font-family: serif;
            }
        </style>
    </head>
    <body>
        <p>
            The following lines should <b>not</b> be rendered with the same
            font.
        </p>
        <span id="test">اختبار النص مائل</span><br>
        <span id="reference">اختبار النص مائل</span><br>
        <script>
            var testRect = document.getElementById('test').
                getBoundingClientRect();
            var referenceRect = document.getElementById('reference').
                getBoundingClientRect();
            var matches = testRect.left == referenceRect.left &&
                testRect.right == referenceRect.right &&
                testRect.height == referenceRect.height;

            if (matches)
                testFailed('The two lines should not match.');
            else
                testPassed('The two lines does not match, as expected.');
        </script>
    </body>
</html>
