<html>
<head>
        <title>Testcase for bugzilla bug 6418</title>
</head>
<body>
    <p>
        This tests for a regression against
        <i><a href="https://bugs.webkit.org/show_bug.cgi?id=6418">http://bugzilla.opendarwin.org/show_bug.cgi?id=6418</a>
        Incorrect scrollbar when using overflow:auto and word-wrap:break-word;
        in some cases</i>.
    </p>
    <hr>
    <p>
        The first line should break after &ldquo;Lorem&rdquo;.
    </p>
    <div style="fonty-family: Times; font-size: 16px; width:83px; border: 1px cyan solid;">
        Lorem ipsum<span> dolor</span>
    </div>
    <hr>
    <p>
        The first line should break after the letter u, so that the gray border does not
        extend beyond the cyan box.
    </p>
    <div style="width: 85px; font-size: 16px; word-wrap:break-word; border: solid 1px cyan; font-family: Times;">
        <span style="border-right: 30px silver solid;">Loremipsum</span>
    </div>
    <hr>
    <p>
        The first line should break after the letter p, so that the text does not
        extend beyond the cyan box.
    </p>
    <div style="width: 85px; font-size: 16px; word-wrap:break-word; border: solid 1px cyan; font-family: Times;">
        <span style="border-left: 30px silver solid;">Loremipsum</span>
    </div>
    <hr>
    <p>
        &ldquo;Dolor&rdquo; should not break into two lines.
    </p>
    <div style="position: absolute;">
        <div style="font-size: 16px; word-wrap:break-word; border: solid 1px cyan; font-family: 'Lucida Grande';">
            Dolor
        </div>
    </div>
</body>
</html>
