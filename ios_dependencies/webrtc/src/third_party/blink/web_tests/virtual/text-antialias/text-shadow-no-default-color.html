<html xmlns="http://www.w3.org/1999/xhtml">
    <head>
        <meta http-equiv="content-type" content="text/html; charset=UTF-8">
        <title>CSS Test: The 'text-shadow' property applied with no color specified</title>
        <script src="../../resources/ahem.js"></script>
        <link rel="author" title="Codeaurora" href="http://www.codeaurora.org/">
        <link rel="help" href="http://www.w3.org/TR/css3-text/#text-shadow">
        <meta name="flags" content="">
        <meta name="assert" content="The 'text-shadow' property should get it's color from the color property if not specified in the text-shadow definition.">
        <style type="text/css">
            #test
            {
                font-size: 30px;
                font-family: Ahem;
                margin: 25px;
                color: green;
                text-shadow: 10px 35px;
            }
        </style>
    </head>
    <body>
        <p>Test passes if there are two green rectangles on the page.</p>
        <div id="test">FillerText</div>
    </body>
</html>
