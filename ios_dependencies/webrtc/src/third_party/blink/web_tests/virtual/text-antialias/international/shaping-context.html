<!DOCTYPE html>
<head>
<meta charset="utf-8">
<style type="text/css">

body {
font-size: 50px;
}

@font-face {
font-family: scheherazadesubset;
src: url("../../../third_party/Scheherazade/Scheherazade-Regular.ttf");
}

/* Declaring this subset for ښ previously broke context for the outer parts of the word
since the middle ښ is rendered with a different font. Similarly, the م would be split
off of تن. */
@font-face {
font-family: scheherazadesubset;
src: url("../../../third_party/Scheherazade/Scheherazade-Regular.ttf");
unicode-range: U+069A,U+062A,U+646;
}

@font-face {
font-family: libertine;
src: url("../../../third_party/Libertine/LinLibertine_R.woff");
unicode-range: U+00-U+FF, U+200D;
}

@font-face {
font-family: scheherazadefull;
src: url("../../../third_party/Scheherazade/Scheherazade-Regular.ttf");
}


.fallbackcontexttest {
font-family: scheherazadesubset;
}

.fallbacklatintoarabic {
font-family: libertine, scheherazadefull;
}

</style>
</head>
<body>
    <div class="fallbackcontexttest">پښتو</div>
    <div class="fallbackcontexttest">متن</div>
    <div class="fallbacklatintoarabic">م&zwj;تن</div>
</body>
