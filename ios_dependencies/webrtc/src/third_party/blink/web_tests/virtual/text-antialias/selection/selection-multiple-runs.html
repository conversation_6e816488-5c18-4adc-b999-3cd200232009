<!DOCTYPE html>
<html lang="ja">
    <head>
        <meta charset="UTF-8">
        <title>Test selection across multiple shaper runs</title>
        <style>
            body { font-size: 30px; }
            ::selection { color: red; background: rgba(120,120,120,120); }
        </style>
    </head>
    <body>
        <p>The selection should be painted correctly from and including 猫 until and including C, then from right to left for العر. </p>
        <div>吾輩は猫で मानक हिन्दीABCالعربية</div>
        <div id="multiruns">吾輩は猫で मानक हिन्दीABCالعربية</div>
        <script>
            var node = document.getElementById('multiruns').firstChild;
            var range = document.createRange();
            range.setStart(node, 3);
            range.setEnd(node, node.length - 3);
            window.getSelection().addRange(range);
        </script>
    </body>
</html>
