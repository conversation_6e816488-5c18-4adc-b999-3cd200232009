<html><head><title></title>
<style type="text/css">
table { border-collapse: collapse; }
td { border: 1px solid green; }
.times { font-family: Times; }
</style>
</head>
<body>
<p>
This is a test for <i>http://bugzilla.opendarwin.org/show_bug.cgi?id=6139
ATSUI code path should implement small caps, synthetic bold and oblique and correct metrics for fallback fonts</i>.
</p>
<hr>
<p>
The two columns should be identical except for the
accent over the e and the umlaut over the u.
</p>
<table style="font-family: Geneva; font-size: 20px;">
<tr>
<td>
Lore&#x0300;m <span class="times">ipsu&#x0308;m</span>
</td>
<td>
Lorem <span class="times">ipsum</span>
</td>
</tr>
<tr style="font-weight: bold;">
<td>
Lore&#x0300;m <span class="times">ipsu&#x0308;m</span>
</td>
<td>
Lorem <span class="times">ipsum</span>
</td>
</tr>
<tr style="font-style: italic;">
<td>
Lore&#x0300;m <span class="times">ipsu&#x0308;m</span>
</td>
<td>
Lorem <span class="times">ipsum</span>
</td>
</tr>
<tr style="font-weight: bold; font-style: italic;">
<td>
Lore&#x0300;m <span class="times">ipsu&#x0308;m</span>
</td>
<td>
Lorem <span class="times">ipsum</span>
</td>
</tr>
<tr style="font-variant: small-caps;" class="times">
<td>
Lore&#x0300;m ipsu&#x0308;m
</td>
<td>
Lorem ipsum
</td>
</tr>
</table>
<hr>
<p>
The following two lines should be identical.
</p>
<!-- Fallback from Times to Lucida Grande -->
<p>
<span style="font-family:Times; font-size: 18px; border: solid green 1px;">
&#x0e01;&#x0e02;&#x0e03;&#x0e04;&#x0e05;&#x0e06;&#x0e07;&#x0e08;&#x0e01;&#x0e02;&#x0e03;&#x0e04;&#x0e05;&#x0e06;&#x0e07;&#x0e08;&#x0e01;&#x0e02;&#x0e03;&#x0e04;&#x0e05;&#x0e06;&#x0e07;&#x0e08;&#x0e01;&#x0e02;&#x0e03;&#x0e04;&#x0e05;&#x0e06;&#x0e07;&#x0e08;
</span>
</p>
<!-- Lucida Grande, no fallback -->
<p>
<span style="font-family:'Lucida Grande'; font-size: 18px; border: solid green 1px;">
&#x0e01;&#x0e02;&#x0e03;&#x0e04;&#x0e05;&#x0e06;&#x0e07;&#x0e08;&#x0e01;&#x0e02;&#x0e03;&#x0e04;&#x0e05;&#x0e06;&#x0e07;&#x0e08;&#x0e01;&#x0e02;&#x0e03;&#x0e04;&#x0e05;&#x0e06;&#x0e07;&#x0e08;&#x0e01;&#x0e02;&#x0e03;&#x0e04;&#x0e05;&#x0e06;&#x0e07;&#x0e08;
</span>
</p>
</body>
</html>
