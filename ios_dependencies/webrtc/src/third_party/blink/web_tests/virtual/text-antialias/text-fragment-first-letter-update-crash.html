<!DOCTYPE html>
<html>
<body>
<style>
#el0 { -webkit-columns: 1; } 
#el2:first-of-type { } 
#el2:first-letter { content: counter(c); } 
#el3 { -webkit-column-span: all; content: counter(c) attr(A); } 
</style>
<script>
if (window.testRunner)
    testRunner.dumpAsText();

document.body.offsetTop;
el0 = document.createElement('div');
el0.setAttribute('id', 'el0');
document.body.appendChild(el0);
el1=document.createElement('b');
el0.appendChild(el1);
el1.appendChild(document.createTextNode('A'));
el2=document.createElement('div');
el2.setAttribute('id','el2');
el0.appendChild(el2);
el3=document.createElement('div');
el3.setAttribute('id', 'el3');
el2.appendChild(el3);
document.designMode = 'on';
document.execCommand('selectall');
el2.appendChild(document.createTextNode('AA'));
document.designMode = 'on';
document.execCommand('selectall');
document.execCommand('removeFormat');
document.body.offsetTop;
document.body.innerHTML = "PASS. WebKit didn't crash.";

</script>
</body>
</html>