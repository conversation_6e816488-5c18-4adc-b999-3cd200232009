<!DOCTYPE html>
<div style="font-size: 72px;">
    <span id="reference">e&#x0300;</span><span id="test">&#x1e17;e&#x0300;</span>
</div>
<div id="result">Test did not run.</div>
<script>
    if (window.testRunner)
        testRunner.dumpAsText();

    var reference = document.getElementById("reference").firstChild;
    var referenceRange = document.createRange();
    referenceRange.setStart(reference, 0);
    referenceRange.setEnd(reference, 2);
    var referenceWidth = referenceRange.getBoundingClientRect().width;

    var test = document.getElementById("test").firstChild;
    var testRange = document.createRange();
    testRange.setStart(test, 1);
    testRange.setEnd(test, 3);
    var testWidth = testRange.getBoundingClientRect().width;

    document.getElementById("result").innerText = Math.abs(testWidth - referenceWidth) <= 1 ? "PASS" : "FAIL";
</script>
