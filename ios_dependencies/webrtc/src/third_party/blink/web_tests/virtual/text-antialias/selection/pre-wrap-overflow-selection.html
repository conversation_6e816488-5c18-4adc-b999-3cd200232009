<html>
<head>
<title>Pre-Wrap Test Page</title>
<script type="text/javascript">
function test()
{
    var t = document.getElementById('t1').childNodes[0];
    var u = document.getElementById('t2').childNodes[0];
    window.getSelection().setBaseAndExtent(u, 7, t, 15);
}
</script>
</head>
<body onload="test()">
<p>
This test checks for a regression against
<i><a href="https://bugs.webkit.org/show_bug.cgi?id=5619">http://bugzilla.opendarwin.org/show_bug.cgi?id=5619</a>
CSS2: pre-wrap has overflow/selection problems</i>.
</p>
<p>
Both boxes should not have scroll bars. The selection should
not spill out of the blue box to the right of &ldquo;text&rdquo;.
</p>
<hr>
<pre id="t2" wrap style="overflow:auto; border:4px solid brown; width:100px">
This   text     will wrap
and   fit   within the
box.
</pre>
<hr>
<pre id="t1" wrap style="border:4px solid blue; width:100px">
This   text     will wrap
and   fit   within the
box.
</pre>
</body>
</html>
