<!DOCTYPE html>
<script src="../../resources/js-test.js"></script>
<style>
div.container {
    font-size: 200px;
    line-height: 1;
}

@font-face {
    /* LinLibertine_R's aspect value = 0.440918 */
    font-family : 'referenceFont';
    src : url('../../third_party/Libertine/LinLibertine_R.woff') format("woff");
}

@font-face {
    /* OpenSans-Regular's aspect = 0.544922 */
    font-family : 'testFont';
    src : url('../../resources/opensans/OpenSans-Regular.woff') format("woff");
}

span.test {
    font-family: testFont;
    font-size-adjust: 0.440;
}

span.reference {
    font-family: referenceFont;
}
</style>

<div class="container">
    <span class="test">x</span><span class="reference">x</span>
</div>

<script>
    description("font-size-adjust should affect neigther computed line-height nor font-size.");

    var test = document.querySelector(".test");
    var reference = document.querySelector(".reference");
    var container = document.querySelector(".container");

    var testStyle = window.getComputedStyle(test, null);
    var referenceStyle = window.getComputedStyle(reference, null);
    var containerStyle = window.getComputedStyle(container, null);

    shouldEvaluateTo("parseFloat(testStyle.getPropertyValue('font-size-adjust'))", 0.440, 0.0001);
    shouldBeEqualToString("referenceStyle.getPropertyValue('font-size-adjust')", 'none');

    shouldBeEqualToString("testStyle.getPropertyValue('font-size')", referenceStyle.getPropertyValue('font-size'));
    shouldBeEqualToString("testStyle.getPropertyValue('line-height')", referenceStyle.getPropertyValue('line-height'));

    shouldBeEqualToString("containerStyle.getPropertyValue('line-height')", referenceStyle.getPropertyValue('line-height'));
</script>
