<html>
<meta charset="UTF-8">
 <style type="text/css">
    @font-face {
        font-family: 'adobevfproto';
        src: url("../../third_party/AdobeVF/AdobeVFPrototype.otf");
    }
    @font-face {
        font-family: 'chromacheck-sbix';
        src: url("../../third_party/ChromaCheck/chromacheck-sbix.woff");
    }
    @font-face {
        font-family: 'chromacheck-cbdt';
        src: url("../../third_party/ChromaCheck/chromacheck-cbdt.woff");
    }

    @font-face {
        font-family: "chromacheck-colr";
        src: url("../../third_party/ChromaCheck/chromacheck-colr.woff");
    }

    .cff2_test {
        font-family: adobevfproto, sans-serif;
    }
    .sbix_test {
        font-family: chromacheck-sbix, sans-serif;
    }
    .cbdt_test {
        font-family: chromacheck-cbdt, sans-serif;
    }

    .colr_test {
        font-family: chromacheck-colr, sans-serif;
    }

    .horizontalgrid {
        display: flex;
    }

    .gridcell {
        flex: 1;
        padding: 10px;
        overflow: visible;
    }

    p {
        margin-bottom: 1em;
    }
 </style>
<body>
    <div class="horizontalgrid">
        <div class="gridcell"><p>Should show as a serif font, not as a sans-serif fallback (CFF2):</p><span class="cff2_test"></span></div>
        <div class="gridcell"><p>Should show red squares (SBIX):</p><span class="sbix_test"></span></div>
        <div class="gridcell"><p>Should show dark red squares (CBDT):</p><span class="cbdt_test"></span></div>
        <div class="gridcell"><p>Should show red squares (COLR):</p><span class="colr_test"></span></div>
    </div>
    <script>
      function appendRotatedScale(theNode, testText) {
          var fontSize = 3;
          var angle = 15;
          for (var i = 0; i < 7; ++i) {
              fontSize = fontSize * 2;
              theDiv = document.createElement("div");
              theDiv.style.position = "absolute";
              theDiv.style.fontSize = fontSize + "px";
              theDiv.style.transformOrigin = "left bottom";
              theDiv.style.transform = "rotateZ(" + angle * i + "deg) translate(" + 10 * i + "px)";
              theDiv.innerText = testText;
              theNode.appendChild(theDiv);
          }
      }

      appendRotatedScale(document.querySelector(".cff2_test"), "y");
      appendRotatedScale(document.querySelector(".sbix_test"), "\uE901");
      appendRotatedScale(document.querySelector(".cbdt_test"), "\uE903");
      appendRotatedScale(document.querySelector(".colr_test"), "\uE900");
     </script>
</body>
</html>
