<!DOCTYPE html>
<html>
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
    <style type="text/css">
      body {
          font-family: "Ahem";
      }
      #kickFallback {
          -webkit-font-smoothing: antialiased;
      }
    </style>
    <title>Font Cache Key and Fallback</title>
</head>
<!--
  Content shell caches fonts across running individual tests,
  which is especially inconvenient for ref testing this
  issue with the font cache. We work around this by making
  use of the fact that in content_shell's font configuration
  the chosen Devanagari fallback font from default sans-serif
  and from "Ahem" are the same, but finding the fallback
  starts from a different internal Font instance.
-->
<body><span id="kickFallback">स</span><span>स्मि</span></body>
</html>
