<!DOCTYPE html>
<script src="../../../resources/testharness.js"></script>
<script src="../../../resources/testharnessreport.js"></script>

<div data-expected="manual" title="Initial value"></div>

<div style="hyphens: auto" data-expected="auto"></div>
<div style="hyphens: manual" data-expected="manual"></div>
<div style="hyphens: none" data-expected="none"></div>

<div style="hyphens: auto">
  <div data-expected="auto" title="hyphens should inherit"></div>
</div>

<script>
Array.prototype.forEach.call(document.querySelectorAll("[data-expected]"), function (element) {
  test(function () {
    var actual = getComputedStyle(element).hyphens;
    assert_equals(actual, element.dataset.expected);
  }, element.title || element.getAttribute("style"));
});
</script>

