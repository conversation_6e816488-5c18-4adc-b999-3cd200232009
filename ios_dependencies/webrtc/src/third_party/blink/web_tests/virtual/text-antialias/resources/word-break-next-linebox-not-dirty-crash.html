<html>
<body onload="runTest();">
<audio>a</audio>a
<center></center>
aaaaaaaaaaaaaaaaaaa
<wbr id="wbr"/>
<span>aaaaaaaaaaaaa</span>
<script>
function reference(domNode)
{
    this.domNode = domNode;
}
function walk(a, currentPrefix, index, domNode)
{
    if (domNode == null)
        return;
    newPrefix = currentPrefix + "_" + index;
    walk(a, currentPrefix, index + 1, domNode.nextSibling);
    walk(a, newPrefix, 0, domNode.firstChild);
    a[newPrefix] = new reference(domNode);
}
function clearAllNodes()
{
    var a = new Array();
    walk(a, "", 0, document.body);
    for (key in a)
    {
        document.body.offsetTop;
        a[key].domNode.parentNode.removeChild(a[key].domNode);
    }
}
function runTest() {
  var font = document.createElement('font');
  document.getElementById('wbr').appendChild(font);
  document.body.offsetTop;
  clearAllNodes();
  
  if (window.testRunner)
      testRunner.notifyDone();
}
</script>
</body>
</html>

