<!DOCTYPE html>
<script src="../../resources/testharness.js"></script>
<script src="../../resources/testharnessreport.js"></script>
<style>
  caption:first-line { visibility: hidden; }
  caption:first-letter { color: red; }
</style>

The test passes if it does not CRASH in debug builds.

<meta id='meta'></meta>
<object id='object'>
  <table>
    <caption dir="rtl">)))TQ?</caption>
  </table>
</object>
<script>
test(() => {
  document.getElementById('object').appendChild(document.getElementById('meta'));
  document.execCommand('FindString', false, 'test');
}, 'Should not crash');
</script>
