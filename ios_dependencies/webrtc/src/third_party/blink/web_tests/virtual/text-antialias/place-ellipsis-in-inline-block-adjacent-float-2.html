<!DOCTYPE html>
<meta charset='utf-8'>
<style>
.container {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  width: 100px;
  height: 50px;
}
.rtl {
    direction: rtl;
}
.float {
  float: right;
  width: 100px;
  height: 50px;
}
span {
  display: inline-block;
}
</style>
<p>crbug.com/720377: The text below should have an ellipsis.</p>
<p>You should see an ellipsis followed by hebrew characters.</p>
<div class="container rtl">
  <span>בגדהוזחטיכךל בגדהוזחטיכךל</span>
  <div class="float" style="float:left;"></div>
</div>
<p>You should see hebrew and latin characters followed by an ellipsis.</p>
<div class="container">
  <div class="float"></div>
  <span>בגדהוזdsjklחטdsaיכךל בגדהוזחטיכךל</span>
</div>
<p>You should see an ellipsis followed by hebrew and latin characters.</p>
<div class="container rtl">
  <div class="float" style="float:left;"></div>
  <span>בגדהוזd sjklחט dsaיכדהו dsaזחטיכךל</span>
</div>
