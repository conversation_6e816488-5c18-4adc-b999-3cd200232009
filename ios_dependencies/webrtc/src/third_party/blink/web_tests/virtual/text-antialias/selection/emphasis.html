<!doctype html>
<meta http-equiv="content-type" content="text/html; charset=UTF-8">
<style>
    body {
        font-size: 24px;
    }

    div {
        border: solid;
        width: 360px;
        display: inline-block;
        margin: 4px;
    }

    span::selection {
        background-color: brown;
        color: yellow;
    }

    #special-span::selection {
        -webkit-text-emphasis-color: lightgreen;
    }
</style>
<div>
    Lorem ipsum dolor sit amet,
    <span style="-webkit-text-emphasis: '@ignored';">consectetur adipiscing</span>
    elit. Aliquam
    <span style="-webkit-text-emphasis: '*'; -webkit-text-emphasis-position: under;">odio sapien</span>,
    lobortis eu iaculis vel, scelerisque nec dolor.
</div>

<div style="text-rendering: optimizelegibility;">
    Lorem ipsum dolor sit amet,
    <span style="-webkit-text-emphasis: '@';">consectetur adipiscing</span>
    elit. Aliquam
    <span style="-webkit-text-emphasis: '*does not matter'; -webkit-text-emphasis-position: under;">odio sa&#x0300;pien</span>,
    lobortis eu iaculis vel, scelerisque nec dolor.
</div>

<div>
    Lorem <span style="-webkit-text-emphasis: filled red;">ipsum</span>
    <span style="-webkit-text-emphasis: open green;">dolor</span>
    <span style="-webkit-text-emphasis: circle;">sit</span>
    <span style="-webkit-text-emphasis: dot;">amet</span>,
    <span style="-webkit-text-emphasis: double-circle;">consectetur</span>
    <span style="-webkit-text-emphasis: sesame;">adipiscing</span>
    <span style="-webkit-text-emphasis: triangle;">elit</span>.

    <span style="-webkit-text-emphasis: open dot;">Aliquam</span>,
    <span style="-webkit-text-emphasis: open double-circle;">odio</span>
    <span style="-webkit-text-emphasis: open sesame;">sapien</span>,
    <span style="-webkit-text-emphasis: open triangle;">lobortis</span>
    eu iaculis vel, scelerisque nec dolor.
</div>

<div style="-webkit-writing-mode: vertical-rl; height:150px;">
    Lorem <span style="-webkit-text-emphasis: filled red;">ipsum</span>
    <span style="-webkit-text-emphasis: open green;">dolor</span>
    <span style="-webkit-text-emphasis: circle;">sit</span>
    <span style="-webkit-text-emphasis: dot;">amet</span>,
    <span style="-webkit-text-emphasis: double-circle;">consectetur</span>
    <span style="-webkit-text-emphasis: sesame;">adipiscing</span>
    <span style="-webkit-text-emphasis: triangle;">elit</span>.

    <span style="-webkit-text-emphasis: open dot;">Aliquam</span>,
    <span style="-webkit-text-emphasis: open double-circle;">odio</span>
    <span style="-webkit-text-emphasis: open sesame;">sapien</span>,
    <span style="-webkit-text-emphasis: open triangle;">lobortis</span>
    eu iaculis vel, scelerisque nec dolor.
</div>

<div style="line-height: 2;">
    Lorem ipsum dolor sit amet,
    <span style="-webkit-text-emphasis: '@';">consectetur adipiscing</span>
    elit. Aliquam
    <span style="-webkit-text-emphasis: '*'; -webkit-text-emphasis-position: under;">odio sapien</span>,
    lobortis eu iaculis vel, scelerisque nec dolor.
</div>

<div>
    Lorem ipsum dolor sit amet,
    <span id="first-span" style="-webkit-text-emphasis: '@';">consectetur adipiscing</span>
    elit. Aliquam
    <span id="special-span" style="-webkit-text-emphasis: '*'; -webkit-text-emphasis-position: under;">odio sapien</span>,
    lobortis eu iaculis vel, scelerisque nec dolor.
</div>

<script>
    getSelection().setBaseAndExtent(document.getElementById("first-span").firstChild, 10, document.getElementById("special-span").firstChild, 7);
</script>
