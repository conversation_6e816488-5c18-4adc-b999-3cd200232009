<!DOCTYPE html>
<script src="../../resources/testharness.js"></script>
<script src="../../resources/testharnessreport.js"></script>
<body>
<p>This test checks various characters that should always be zero width to ensure that they are.
The WebKit text system ensures this in a way that's independent of the fonts installed on the system.</p>
<p id="testArea"><span id="characters">ab</span></p>
<script>
run();
function run() {
  var span = document.getElementById("characters");
  var abWidth = span.offsetWidth;
  test(() => {
    span.firstChild.data = "a";
    var aWidth = span.offsetWidth;
    assert_greater_than(abWidth, aWidth, "Check width measurement");
  }, "Check width measurement");

  const testCharacters = [
    0x200B,
    0x200C,
    0x200D,
    0x200E,
    0x200F,
    0xFEFF
  ];
  for (let testCharacter of testCharacters) {
    test(() => {
      span.firstChild.data = "a" + String.fromCharCode(testCharacter) + "b";
      var abWithCharactersWidth = span.offsetWidth;
      assert_equals(abWithCharactersWidth, abWidth);
    }, `U+${testCharacter.toString(16).toUpperCase()} should be zero-width`);
  }
}
</script>
</body>
