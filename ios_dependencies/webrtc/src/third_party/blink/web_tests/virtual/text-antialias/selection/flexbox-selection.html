<!DOCTYPE html>
<div style="display: flex; background-color: #ddd; font-size: 24pt">
  <div style="width: 100px">
    <span id="a">aaaa</span><br>
    <span id="b">bbbb</span>
  </div>
  <div style="width: 100px">
    <span id="c">cccc</span><br>
    <span id="d">dddd</span>
  </div>
</div>
<script>

var b = document.querySelector('#b'),
    d = document.querySelector('#d');
getSelection().setBaseAndExtent(b.firstChild, 0, d.firstChild, 2);
focus();

</script>
