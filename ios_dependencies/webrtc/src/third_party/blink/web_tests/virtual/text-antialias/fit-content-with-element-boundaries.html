<!DOCTYPE html>
<script src="../../resources/testharness.js"></script>
<script src="../../resources/testharnessreport.js"></script>
<style>
div {
  display: inline-block;
  font-family: Consolas, 'Courier New', Courier, monospace;
  line-height: 1;
}
img {
  width: 1em;
  height: 1em;
}
</style>

<div title="img (!m_uncommittedWidth)"><img src="../replaced/resources/1x1-blue.png"><img src="../replaced/resources/1x1-blue.png"><img src="../replaced/resources/1x1-blue.png"><img src="../replaced/resources/1x1-blue.png"><img src="../replaced/resources/1x1-blue.png"><img src="../replaced/resources/1x1-blue.png"><img src="../replaced/resources/1x1-blue.png"><img src="../replaced/resources/1x1-blue.png"><img src="../replaced/resources/1x1-blue.png"><img src="../replaced/resources/1x1-blue.png"></div>

<br>

<div title="text node (end with m_uncommittedWidth)"><span>a </span>a <span>a </span>a <span>a </span>a <span>a </span>a <span>a </span>a <span>a </span>a <span>a </span>a <span>a </span>a <span>a </span>a <span>a </span>a</div>

<br>

<div title="text node (end with !m_uncommittedWidth)"><span>a  </span>a  <span>a  </span>a  <span>a  </span>a  <span>a  </span>a  <span>a  </span>a  <span>a  </span>a  <span>a  </span>a  <span>a  </span>a  <span>a  </span>a  <span>a  </span>a  </div>

<br>
<div title="Real case from inspector"><span>(9) </span>[<span>1</span>, <span>2</span>, <span>3</span>, <span>4</span>, <span>5</span>, <span>6</span>, <span>7</span>, <span>8</span>, <span>9</span>]</div>

<script>
runTests();
function runTests() {
  const from = 5, to = 20;
  for (let element of document.getElementsByTagName("div")) {
    test(() => {
      for (let i = from; i <= to; i++) {
        element.style.fontSize = i + "px";
        let height = element.offsetHeight;
        assert_approx_equals(height, i, i / 2, `Line should not wrap at font-size: ${i}px`);
      }
    }, `${element.title} from ${from}px to ${to} px`);
  }
}
</script>
