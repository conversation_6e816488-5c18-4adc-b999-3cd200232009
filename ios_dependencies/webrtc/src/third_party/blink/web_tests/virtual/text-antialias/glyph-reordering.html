<p>
    This tests text selection in complex scripts where glyph reordering occurs.
</p>
<div id="target" style="font-size: 48px;"><span>[</span>&#x0939;&#x093F;&#x0928;&#x094D;&#x0926;&#x0940;<span>]</span></div>
<div id="reference" style="font-size: 48px;"><span>[</span>&#x0928;&#x094D;&#x0926;&#x0940;<span>]</span></div>
<p id="result">Test did not run</p>
<script>
    if (window.testRunner)
        testRunner.dumpAsText();

    var target = document.getElementById("target");

    var textNode = target.firstChild.nextSibling;
    var range = document.createRange();
    range.setStart(textNode, 0);
    range.setEnd(textNode, 2);
    var width = range.getClientRects()[0].width;

    range.setStart(target, 0);
    range.setEnd(target, 3);
    var totalWidth = range.getBoundingClientRect().width;

    var reference = document.getElementById("reference");
    range.setStart(reference, 0)
    range.setEnd(reference, 3);
    var referenceWidth = totalWidth - range.getBoundingClientRect().width;

    document.getElementById("result").innerText = Math.abs(width - referenceWidth) < 0.5 ? "PASS" : "FAIL: width was " + width + " instead of " + referenceWidth;
</script>
