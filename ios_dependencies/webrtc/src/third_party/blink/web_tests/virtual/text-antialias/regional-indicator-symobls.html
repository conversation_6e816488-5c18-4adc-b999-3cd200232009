<style>
    span { border: solid; }
    span#reference { text-rendering: optimizelegibility; }
</style>
<p>
    Test that regional indicator symobol letters can combine into national flags.
</p>
<p>
    The two boxes below should look the same.
</p>
<p id="result">
    FAIL: Test did not finish.
</p>
<p style="font-size: 48px;">
    <span id="test">&#x1f1ec;&#x1f1e7; &#x1f1fa;&#x1f1f8;</span>
    <span id="reference">&#x1f1ec;&#x1f1e7; &#x1f1fa;&#x1f1f8;</span>
</p>
<script>
    if (window.testRunner)
        testRunner.dumpAsText();

    var test = document.getElementById("test");
    var reference = document.getElementById("reference");
    document.getElementById("result").innerText = test.offsetWidth === reference.offsetWidth ? "PASS" : "FAIL";
</script>
