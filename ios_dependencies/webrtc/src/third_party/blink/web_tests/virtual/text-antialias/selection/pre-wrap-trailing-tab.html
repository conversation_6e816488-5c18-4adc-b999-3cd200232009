<!DOCTYPE html>
<html>
<body><div id="tests"><textarea style="width: 1ex; font-size: 1em;"
></textarea></div><pre id="log"></pre><script>

function assertEqual(name, length, endOffset)
{
    log.textContent += '\n' + name + ': ';
    if (length != endOffset)
        log.textContent += 'FAIL - length was ' + length + ' but selection end was ' + endOffset + ' after selecting all text';
    else
        log.textContent += 'PASS';
}

var tests = document.getElementById('tests').childNodes;
var log = document.getElementById('log');
log.textContent = 'This test ensures WebKit renders the trailing whitespace properly. You should see a PASS below.\n';

var letter = 'a';
for (var i = 0; i < tests.length; i++, letter = String.fromCharCode(letter.charCodeAt(0) + 1)) {
    tests[i].value = letter + '\t';
    tests[i].focus();
    tests[i].select();
    var endOffset = tests[i].selectionEnd;
    assertEqual('test ' + i, 2, endOffset);
}

if (window.testRunner) {
    testRunner.dumpAsText();
    document.getElementById('tests').style.display = 'none';
}

</script></pre></body>
</html>
