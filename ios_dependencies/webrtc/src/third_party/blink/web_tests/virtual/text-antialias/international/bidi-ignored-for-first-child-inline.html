<html>
<head>
<title>Bidi properties ignored for inline container whose first child is an inline container</title>
<style type="text/css">
.rlo { direction: rtl; unicode-bidi: bidi-override; }
[dir] { unicode-bidi: embed; }
</style>
</head>
<body>
<p>
This is a test for <i>http://bugzilla.opendarwin.org/show_bug.cgi?id=5980 Bidi properties of an inline container whose first child is an inline container are ignored</i>.
</p>
<hr>
<p>
The following lines should read &ldquo;ABCDEFGHI&rdquo;:
</p>
<p>
ABC<span class="rlo">FED</span>GHI
</p>
<p>
ABC<span class="rlo"><span>FED</span></span>GHI
</p>
<p>
ABC<span class="rlo">F<span>ED</span></span>GHI
</p>
<p>
ABC<span class="rlo">F<span>E</span>D</span>GHI
</p>
<p>
ABC<span class="rlo"><span>FE</span>D</span>GHI
</p>
<p><span class="rlo">IHGFEDCBA</span>
</p>
<p>
<span class="rlo">IHGFEDCBA</span>
</p>
<hr>
The following lines should be identical:
<p>
&#x05d3;&#x05d4;&#x05d5;([&#x202a;&#x202c;&#x05d0;&#x05d1;&#x05d2;
</p>
<p>
&#x05d3;&#x05d4;&#x05d5;([<span dir="ltr"></span>&#x05d0;&#x05d1;&#x05d2;
</p>
<p>
&#x05d3;&#x05d4;&#x05d5;([<span dir="ltr"><span></span></span>&#x05d0;&#x05d1;&#x05d2;
</p>
<p>
&#x05d3;&#x05d4;&#x05d5;([<span><span dir="ltr"></span></span>&#x05d0;&#x05d1;&#x05d2;
</p>
</body>
</html>
