<html>
<head>
    <title>Test HTML Page</title>
<script src="../../resources/run-after-layout-and-paint.js"></script>
<script>
function test()
{
    if (window.testRunner) {
        testRunner.dumpAsText();
        testRunner.waitUntilDone();
        runAfterLayoutAndPaint(function() {
            testRunner.notifyDone();
        });
    }
}
</script>
    <style type="text/css">
    p { text-shadow: purple 0px 0px 38005685px; }
    </style>
</head>
<body onload="test()">
    <p>Extreme text-shadow blur values should not crash.</p>
</body>
</html>
