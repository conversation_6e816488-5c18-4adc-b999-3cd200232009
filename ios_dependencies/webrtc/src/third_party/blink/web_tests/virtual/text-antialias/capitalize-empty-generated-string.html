<html>
<head>
    <title></title>
    <style type="text/css">
        span.cap { text-transform: capitalize; }
        span.gen:before { content: ""; }
    </style>
</head>
<body>
    <p>
        This is a regression test for <i><a href="https://bugs.webkit.org/show_bug.cgi?id=9432">http://bugzilla.opendarwin.org/show_bug.cgi?id=9432</a>
        REGRESSION: crash in capitalization code due to empty-string generated content</i>.
    </p>
    <hr>
    <p>
        <span class="cap">lorem <span class="gen">ipsum</span></span>
        <i>should be</i> Lorem Ipsum
    </p>
    <p>
        <span class="cap">lor<span class="gen">em ipsum</span></span>
        <i>should be</i> Lorem Ipsum
    </p>
</body>
