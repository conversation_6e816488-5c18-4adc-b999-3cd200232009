<!DOCTYPE html>
<script src="../../../resources/testharness.js"></script>
<script src="../../../resources/testharnessreport.js"></script>
<div dir="auto" id="container">
    <span id="inner">&#x680;</span>
    ABC DEF GHI JKL
</div>
<script>
test(function () {
    assert_equals(window.getComputedStyle(container).getPropertyValue("direction"), "rtl");
}, "dir=auto should pick the first character in the first span");
inner.dir = "rtl";
test(function () {
    assert_equals(window.getComputedStyle(container).getPropertyValue("direction"), "ltr");
}, "dir=auto should skip span with explicit dir set");
if (window.testRunner)
    container.style.display = "none";
</script>
