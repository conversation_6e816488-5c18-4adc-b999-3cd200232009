<!DOCTYPE html>
<html>
<head>
<style>
@font-face {
  font-family: Libertine;
  src: url("../../third_party/Libertine/LinLibertine_R.woff");
}

body {
  margin: 0px;
}

div {
  background-color: lightgreen;
  position: absolute;
  left: -500px;
  width: 1000px;
}

p {
  font-size: 30px;
  font-family: Libertine;
  width: 500px;
  background-color: lightpink;
  color: red;
  -webkit-font-feature-settings: 'smcp';
  font-feature-settings: 'smcp';
}
</style>
</head>
<body>
Testing for incorrect text overflow when using small caps in the complex test path. There should be no red characters visible on the green rectangle.
<div><p>Lorem ipsum dolor sit amet, consectetuer adipiscing elit, sed diam nonummy nibh euismod tincidunt ut laoreet dolore magna aliquam erat volutpat. Ut wisi enim ad minim veniam, quis nostrud exerci tation ullamcorper suscipit lobortis nisl ut aliquip ex ea commodo consequat.</p></div>
</body>
</html>
