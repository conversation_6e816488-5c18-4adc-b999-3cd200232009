<html>
<head>
    <title></title>
    <style>
        #target::selection { color: green; }
    </style>
</head>
<body>
    <p>
        Test for <i><a href="http://bugs.webkit.org/show_bug.cgi?id=11124">http://bugs.webkit.org/show_bug.cgi?id=11124</a>
        REGRESSION (r14297): No drag image for partially-selected complex text</i>.
    <p>
        This should look like &ldquo;<span style="color: green;">Lore&#x300;m ipsum</span>&rdquo;: <span id="target">&nbsp;Lore&#x300;m ipsum</span>
    </p>
    <script>
        var text = document.getElementById("target").firstChild;
        window.getSelection().setBaseAndExtent(text, 1, text, 13);
    </script>
</body>
</html>
