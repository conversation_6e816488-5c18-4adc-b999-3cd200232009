﻿<html>
<body>
<p>Three different sizes. The large size in particular should appear detailed and not pixelated.
<p>🙉😻🐑🍼🍲😍<span style="font-size: 5px;">🙉😻🐑🍼🍲😍</span> <span style="font-size: 72px;">🙉😻🐑🍼🍲😍</span></p>
<p>For the following, the emoji colors should not change.<br>
The monkey should be colored, the sheep should be black outlined with a white body, etc.</p>
<p style="font-size: 24px; background-color: blue; width: 400px;">blue background: 🙉😻🐑🍼🍲😍</p>
<p style="font-size: 24px; color: red;">red text: 🙉😻🐑🍼🍲😍</p>
<div style="-webkit-transform: rotate(-45deg); width: 200px; height: 200px; left: 500px; top: 200px; position:absolute;">
<p style="font-size: 24px;">rotated emoji: 🙉😍<br><span style="color: green;">green text: 😻🍼</span><br><span style="background-color: purple;">purple bg: 🐑🍲</span></p>
</div>
<p>For the following, each emoji should appear in two different skin tones.<br>
<p style="font-size: 24px; font-family: Noto Color Emoji;">zero-width-joiners: ✌&#x1f3ff;✌&#x1f3fc;👋&#x1f3ff;👋&#x1f3fc;🤘&#x1f3ff;🤘&#x1f3fc;</p>
</body>
</html>
