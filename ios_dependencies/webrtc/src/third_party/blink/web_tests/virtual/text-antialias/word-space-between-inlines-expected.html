<!DOCTYPE html>
<html>
<head>
<style>
div {
  word-spacing: 1px;
}
.float {
  float: left;
}
.inline-block {
  display: inline-block;
}
</style>
</head>
<body>
<p>This test ensures that preferred width and layout match for blocks with word-spacing between inlines.
All the text below should be on one line.</p>
<div class="float" style="background-color: red">a b</div>
<div class="float" style="background-color: orange">c d</div>
<div class="float" style="background-color: yellow">e f</div>
<div class="float" style="background-color: green">g h</div>
<div class="inline-block" style="background-color: blue">i j
</div><div class="inline-block" style="background-color: indigo">k l
</div><div class="inline-block" style="background-color: purple">m n
</div><div class="inline-block" style="background-color: violet">o p
</div>
</body>