<html>
<head>
<meta charset="utf-8" />
<style type="text/css">
@font-face {
font-family: linlibertine;
src: url("../../../third_party/Libertine/LinLibertine_R.woff");
}
div:lang(ar) {
font-family: linlibertine;
}
</style>
<script>
if (window.testRunner)
    testRunner.dumpAsText();
</script>
</head>
<body>
<p>
This test ensures that combining marks apper over the previous (or next) character by comparing the length of
words (one has marks, and the other doesn't have marks).
</p>

<div style="font-family: arial; text-rendering: optimizeLegibility;">
<span id="reference1">test проверка</span>
</div>
<div style="font-family: arial;">
<span id="target1">te&#768;st прове&#768;рка</span>
</div>

<div lang="ar">
<span id="reference2">خانه</span>
</div>
<div lang="ar">
<span id="target2">خانهٔ</span>
</div>

<div id="result"></div>

<script>
var reference1 = document.getElementById('reference1');
var target1 = document.getElementById('target1');
var reference2 = document.getElementById('reference2');
var target2 = document.getElementById('target2');
var result = document.getElementById('result');

// Ignore a slight diff of the width between target and reference.
var diff1 = Math.abs(reference1.offsetWidth - target1.offsetWidth);
var diff2 = Math.abs(reference2.offsetWidth - target2.offsetWidth);
if (diff1 < 2 && diff2 < 2)
    result.innerHTML = 'PASS. The length of targets and references are the same.<br />'
else
    result.innerHTML = 'FAIL<br />' +
                       'reference1.offsetWidth = ' + reference1.offsetWidth + '<br />' +
                       'target1.offsetWidth = ' + target1.offsetWidth + '<br />' +
                       'reference2.offsetWidth = ' + reference2.offsetWidth + '<br />' +
                       'target2.offsetWidth = ' + target2.offsetWidth + '<br />'
</script>

</body>
</html>
