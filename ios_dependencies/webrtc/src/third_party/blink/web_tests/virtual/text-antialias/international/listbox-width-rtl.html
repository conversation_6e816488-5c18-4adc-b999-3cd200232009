<!DOCTYPE html>
<html>
    <head>
        <style>
            select, span { font: menu; }
        </style>
        <script src="../../../resources/js-test.js"></script>
    </head>
    <body>
        <p>
            Test that text directionality is taken into account when
            sizing list boxes.
        </p>
        <select id="dropdown">
            <option dir="rtl">&#x0627;&#x0628; &#x0627;&#x0628;</option>
        </select>
        <select id="list" size="5">
            <option dir="rtl">&#x0627;&#x0628; &#x0627;&#x0628;</option>
        </select>
        <script>
            function widthForEmptySelect(size)
            {
                var selectElement = document.createElement('select');
                selectElement.size = size;
                document.body.appendChild(selectElement);
                var selectWidth = selectElement.getBoundingClientRect().width;
                document.body.removeChild(selectElement);
                return selectWidth;
            }
            
            function widthForText(str)
            {
                var textElement = document.createElement('span');
                textElement.appendChild(document.createTextNode(str));
                document.body.appendChild(textElement);
                var textWidth = textElement.getBoundingClientRect().width;
                document.body.removeChild(textElement);
                return textWidth;
            }

            function testListbox(id)
            {
                var element = document.getElementById(id);
	        var dropDownStyle = window.getComputedStyle(element.firstChild.nextSibling);
                var paddingRight = dropDownStyle["padding-right"];
	        paddingRight = parseInt(paddingRight.substring(0, paddingRight.length - 2));
                var paddingLeft = dropDownStyle["padding-left"];
	        paddingLeft = parseInt(paddingLeft.substring(0, paddingLeft.length - 2));
	        var padding = paddingRight + paddingLeft;
                var emptyWidth = widthForEmptySelect(element.size);
                var textWidth = widthForText(element.firstElementChild.textContent);
                var dropdownWidth = element.getBoundingClientRect().width;
                var expectedWidth = emptyWidth + textWidth + padding;
                if (Math.abs(dropdownWidth - expectedWidth) <= 1)
                    testPassed('Width of ' + id + ' matches width of text plus width of empty ' + id + '.');
                else
                    testFailed('Width ' + id + ' was ' + dropdownWidth + 'px wide, expected ' + expectedWidth + 'px (' + emptyWidth + 'px for empty ' + id + ' and ' + textWidth + 'px for text).');
            }
            
            testListbox('list');
        </script>
    </body>
</html>
