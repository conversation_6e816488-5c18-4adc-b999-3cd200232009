﻿<html><body>
Pixel test for complex text rendering with opacity (Chromium bug <a href="http://code.google.com/p/chromium/issues/detail?id=8768">8768</a>).<p>
<div style="font-size:25px;">
<span>खोजें</span>
<span style="opacity:0.9;">खोजें</span>
<span style="opacity:0.8;">खोजें</span>
<span style="opacity:0.7;">खोजें</span>
<span style="opacity:0.6;">खोजें</span>
<span style="opacity:0.5;">खोजें</span>
<span style="opacity:0.4;">खोजें</span>
<span style="opacity:0.3;">खोजें</span>
<span style="opacity:0.2;">खोजें</span>
<span style="opacity:0.1;">खोजें</span>
<span style="opacity:0.0;">खोजें</span>
</div>
<div style="font-size:25px;">
<span>يؤلمني</span>
<span style="opacity:0.9;">يؤلمني</span>
<span style="opacity:0.8;">يؤلمني</span>
<span style="opacity:0.7;">يؤلمني</span>
<span style="opacity:0.6;">يؤلمني</span>
<span style="opacity:0.5;">يؤلمني</span>
<span style="opacity:0.4;">يؤلمني</span>
<span style="opacity:0.3;">يؤلمني</span>
<span style="opacity:0.2;">يؤلمني</span>
<span style="opacity:0.1;">يؤلمني</span>
<span style="opacity:0.0;">يؤلمني</span>
</div>
</body></html>
