<!DOCTYPE html>
<html>
  <head>
    <meta charset="utf-8" />
    <title>HTML Test: textarea with dir=auto, all-N between all-Rs</title>
    <link rel="reference" href="dir_auto-textarea-N-between-Rs-ref.html" />
    <link rel="author" title="<PERSON><PERSON><PERSON>" href="mailto:<EMAIL>" />
    <link rel="author" title="HTML5 bidi test WG" href="mailto:<EMAIL>" />
    <link rel="help" href="http://dev.w3.org/html5/spec/Overview.html#the-dir-attribute" />
    <link rel="help" href="http://dev.w3.org/csswg/css3-writing-modes/#unicode-bidi0" />
    <meta name="assert" content="
      When dir='auto', the direction is set according to the first strong character
      of the text.
      For textarea and pre elements, the heuristic is applied on a per-paragraph level.
      If there is no strong character, as in this test, the direction defaults to LTR." />
    <style>
      body, textarea {
        font-size:18px;
        text-align:left;
      }
      .test {
        border: medium solid gray;
        width: 400px;
        margin: 20px;
      }
      .comments {
        display: none;
      }
    </style>
  </head>
  <body>
    <div class="instructions"><p>Test passes if the two boxes below look exactly the same.</p></div>
    <div class="comments">
      Key to entities used below:
        &#x05D0; - The Hebrew letter Alef (strongly RTL).
      We use text-align:left because neither the dir="auto" nor the unicode-bidi:plaintext
      specification states whether text-align:start and text-align:end should obey the paragraph
      direction or the direction property in a unicode-bidi:plaintext element.
      The ...! paragraph, being neutral, is supposed to be displayed LTR (i.e. as ...!, not as !...)
      despite both the paragraph before it and the paragraph after it being all-RTL, which makes the
      element as a whole RTL.
    </div>
    <div class="test">
      <div dir="ltr">
        <textarea rows="4" dir="auto">
&#x05D0;
...!
&#x05D0;
        </textarea>
      </div>
      <div dir="rtl">
        <textarea rows="4" dir="auto">
&#x05D0;
...!
&#x05D0;
        </textarea>
      </div>
    </div>
  </body>
</html>
