<html>
<head>
    <script>
        function canFind(target, specimen)
        {
            getSelection().empty();
            document.body.innerHTML = specimen;
            document.execCommand("FindString", false, target);
            var result = getSelection().rangeCount != 0;
            getSelection().empty();
            return result;
        }

        var messages = "";

        function testTestStyle(isExpectedToFind, style, outerStyle)
        {
            var markup = "<div style='" + style + "'>word</div>";
            var styleMessage = "styled with " + style;

            if (outerStyle) {
                markup = "<div style='" + outerStyle + "'>" + markup + "</div>";
                styleMessage += " inside an element styled with " + outerStyle;
            }

            if (canFind("word", markup) == isExpectedToFind)
                return;

            if (isExpectedToFind)
                messages += " Could not find";
            else
                messages += " Found";
            messages += " a word " + styleMessage + ".";
        }

        function testNonHiddenTextStyle(style, outerStyle)
        {
            testTestStyle(true, style, outerStyle);
        }

        function testHiddenTextStyle(style, outerStyle)
        {
            testTestStyle(false, style, outerStyle);
        }

        function runTests()
        {
            if (window.testRunner)
                testRunner.dumpAsText();

            testNonHiddenTextStyle("");

            testHiddenTextStyle("display:none");
            testHiddenTextStyle("visibility:hidden");

            testNonHiddenTextStyle("height:0");

            testNonHiddenTextStyle("height:0; overflow:hidden");
            testNonHiddenTextStyle("height:0; overflow:scroll");
            testNonHiddenTextStyle("height:0; overflow:auto");

            testNonHiddenTextStyle("width:0; overflow:hidden");
            testNonHiddenTextStyle("width:0; overflow:scroll");
            testNonHiddenTextStyle("width:0; overflow:auto");

            testNonHiddenTextStyle("height:0; overflow-x:hidden");
            testNonHiddenTextStyle("height:0; overflow-x:scroll");
            testNonHiddenTextStyle("height:0; overflow-x:auto");

            testNonHiddenTextStyle("width:0; overflow-x:hidden");
            testNonHiddenTextStyle("width:0; overflow-x:scroll");
            testNonHiddenTextStyle("width:0; overflow-x:auto");

            testNonHiddenTextStyle("height:0; overflow-y:hidden");
            testNonHiddenTextStyle("height:0; overflow-y:scroll");
            testNonHiddenTextStyle("height:0; overflow-y:auto");

            testNonHiddenTextStyle("width:0; overflow-y:hidden");
            testNonHiddenTextStyle("width:0; overflow-y:scroll");
            testNonHiddenTextStyle("width:0; overflow-y:auto");

            testNonHiddenTextStyle("position: relative", "height:0; overflow:hidden");
            testNonHiddenTextStyle("position: relative", "height:0; overflow:scroll");
            testNonHiddenTextStyle("position: relative", "height:0; overflow:auto");

            testNonHiddenTextStyle("position: absolute", "height:0; overflow:hidden");
            testNonHiddenTextStyle("position: absolute", "height:0; overflow:scroll");
            testNonHiddenTextStyle("position: absolute", "height:0; overflow:auto");

            testNonHiddenTextStyle("position: fixed", "height:0; overflow:hidden");
            testNonHiddenTextStyle("position: fixed", "height:0; overflow:scroll");
            testNonHiddenTextStyle("position: fixed", "height:0; overflow:auto");

            if (messages === "")
                messages = "SUCCESS: Found all the strings we expected to, and none we did not expect to.";
            else
                messages = "FAILURE:" + messages;

            document.body.innerHTML = messages;
        }
    </script>
</head>
<body onload="runTests()"></body>
</html>
