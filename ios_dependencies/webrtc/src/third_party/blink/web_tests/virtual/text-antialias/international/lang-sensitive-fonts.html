<!DOCTYPE html>
<html>
<head>
<script>
if (window.internals) {
    internals.settings.setStandardFontFamily("<PERSON><PERSON>", "<PERSON>");
    internals.settings.setFantasyFontFamily("<PERSON><PERSON>", "<PERSON>");
}
</script>
</head>
<body>
<!-- Test for language-sensitive font selection (bug 10874). Some fonts for
Simplified Han script is set to Ahem and the divs are set to language zh_CN
which corresponds to that script. The test passes if all divs are in Ahem
font, except for the final div as xml:lang should be ignored in HTML.
-->
<div style="font-size: 20px">
<div style="font-family: 'Ahem'">this is ahem font</div>
<div lang="zh-CN">this is ahem font</div>
<div id="target1" lang="ar">this is ahem font</div>
<div id="target2" lang="ar" style="font-family: sans-serif">this is ahem font</div>
<div xml:lang="zh-CN">this is not ahem font</div>
</div>
<script>
document.getElementById("target1").lang = "zh-CN";

document.getElementById("target2").lang = "zh-CN";
document.getElementById("target2").style.fontFamily = "fantasy";
</script>
</body>
</html>
