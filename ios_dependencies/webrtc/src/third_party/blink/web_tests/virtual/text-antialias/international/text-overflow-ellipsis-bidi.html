<!DOCTYPE html>
<html>
    <head>
        <style>
            span {
                display: inline-block;
                font: menu;
                text-overflow: ellipsis;
                overflow: hidden;
                background: pink;
                position: absolute;
                top: 100px;
                border: 1px solid white;
            }
        </style>
    </head>
    <body>
        <p>
            Test that text directionality is taken into account when
            sizing inlines with text-overflow ellipsis. None of the
            boxes below should have ellipsis.
        </p>
        <span>&#x0627;&#x0628; &#x0627;&#x0628;</span>
        <span>&#x0627;&#x0628;(&#x0627;&#x0628;)</span>
        <span>AB&#x0627;&#x0628;|&#x0627;&#x0628;|GH</span>
        <span>(&#x0627;&#x0628;)(&#x0627;&#x0628;)</span>
    </body>
</html>
