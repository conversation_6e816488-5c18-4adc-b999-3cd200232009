<!DOCTYPE html>
<html>
    <head>
        <title>Wrapping at element boundaries</title>
        <style>
            body {
                font-family: Verdana, Arial, sans-serif;
                font-size: 75%;
            }
            span {
                background-color: yellow;
            }
            button {
                min-height: 2em;
                min-width: 4em;
                padding-top: 1px;
                padding-bottom: 1px;
                font: inherit;
            }
            span {
                font: inherit;
                background-color: yellow;
            }
        </style>
    </head>
    <body>
        <button id="manage-default-search-engines" i18n-content="defaultSearchManageEngines">Manage s<span class="search-highlighted">earch engines</span>...</button>
        <button id="manage-default-search-engines" i18n-content="defaultSearchManageEngines">Manage <span class="search-highlighted">s</span>earch engines...</button>
        <p>
            Test passes if neither button wraps.
        </p>
    </body>
</html>
