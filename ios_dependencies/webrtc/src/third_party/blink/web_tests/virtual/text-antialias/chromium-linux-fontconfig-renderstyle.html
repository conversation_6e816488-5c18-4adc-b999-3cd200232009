<html>
  <body>
    <p>This test requires Chromium Linux <tt>test_shell</tt> in
      <tt>--layout-test</tt> mode, as that forces some rendering settings used
      in the following tests.</p>

    <ol>
      <li>
        <p>The following text should render without anti-aliasing:</p>
        <p style="font-family: NonAntiAliasedSans">Non anti-aliased sans.</p>
      </li>
      <li>
        <p>The following text should be slightly-hinted Georgia.  The dots
          should be equally spaced, and letters in the word "government"
          should be naturally spaced (without an ugly space before the "e").</p>

        <p style="font-family:SlightHintedGeorgia">
          government ................................
        </p>
      </li>

      <li>
        <p>The following text should be unhinted Verdana. The fontconfig
          configuration for this is contradictory, setting both full-hinting
          and no-hinting. The latter should win out.</p>

        <p style="font-family:NonHintedSans">
          Here is <PERSON>ig<PERSON> doing what tiggers do best &hellip; operating
          hydraulic exoskeletons.
        </p>
      </li>

      <li>
        <p>The following text should show a difference caused by forcing
        autohinting. Note: the effect is subtle.</p>

        <p><span style="font-family:AutohintedSerif; font-size:0.6em;">autohinted</span> <i>vs</i> <span style="font-family:HintedSerif; font-size:0.6em;">not-autohinted</span></p>
      </li>

      <li>
        <p>The following text should be the same. It verifies that, given the contradictory settings <tt>hintfull</tt> and <tt>autohint</tt>, the latter wins out:</p>

        <p><span style="font-family:AutohintedSerif; font-size:0.6em;">autohinted</span> <i>vs</i> <span style="font-family:FullAndAutoHintedSerif; font-size:0.6em;">hopefully autohinted</span></p>
      </li>

      <li>
        <p>The following text should show that fontconfig can be used to enable
        or disable subpixel rendering.</p>

        <p><span style="font-family:SubpixelEnabledArial">subpixel</span> <i>vs</i> <span style="font-family:SubpixelDisabledArial">no subpixel</span></p>
      </li>
    </ol>
  </body>
</html>
