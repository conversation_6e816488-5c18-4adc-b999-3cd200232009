// Test case adapted from Mozilla tests for font-variant-subproperties available under
// http://creativecommons.org/publicdomain/zero/1.0/
// See discussion on https://bugzilla.mozilla.org/show_bug.cgi?id=1261445

/* This file is autogenerated by makegsubfonts.py */

/*
  Features defined in gsubtest fonts with associated base
  codepoints for each feature:

    cp = codepoint for feature featX

    cp   default   PASS
    cp   featX=1   FAIL
    cp   featX=2   FAIL

    cp+1 default   FAIL
    cp+1 featX=1   PASS
    cp+1 featX=2   FAIL

    cp+2 default   FAIL
    cp+2 featX=1   FAIL
    cp+2 featX=2   PASS

*/

var gFeatures = {
  "MWL1": 0xe000, "NUM2": 0xe004, "PRIV": 0xe008, "T3ST": 0xe00c,
  "TPSP": 0xe010, "abvf": 0xe014, "abvm": 0xe018, "abvs": 0xe01c,
  "afrc": 0xe020, "akhn": 0xe024, "blwf": 0xe028, "blwm": 0xe02c,
  "blws": 0xe030, "c2pc": 0xe034, "c2sc": 0xe038, "calt": 0xe03c,
  "case": 0xe040, "ccmp": 0xe044, "cfar": 0xe048, "cjct": 0xe04c,
  "clig": 0xe050, "cpct": 0xe054, "cpsp": 0xe058, "cswh": 0xe05c,
  "curs": 0xe060, "cv00": 0xe064, "cv01": 0xe068, "cv02": 0xe06c,
  "cv03": 0xe070, "cv04": 0xe074, "cv05": 0xe078, "cv06": 0xe07c,
  "cv07": 0xe080, "cv08": 0xe084, "cv09": 0xe088, "cv10": 0xe08c,
  "cv11": 0xe090, "cv12": 0xe094, "cv13": 0xe098, "cv14": 0xe09c,
  "cv15": 0xe0a0, "cv16": 0xe0a4, "cv17": 0xe0a8, "cv18": 0xe0ac,
  "cv19": 0xe0b0, "cv20": 0xe0b4, "cv21": 0xe0b8, "cv22": 0xe0bc,
  "cv23": 0xe0c0, "cv24": 0xe0c4, "cv25": 0xe0c8, "cv26": 0xe0cc,
  "cv27": 0xe0d0, "cv28": 0xe0d4, "cv29": 0xe0d8, "cv30": 0xe0dc,
  "cv31": 0xe0e0, "cv32": 0xe0e4, "cv33": 0xe0e8, "cv34": 0xe0ec,
  "cv35": 0xe0f0, "cv36": 0xe0f4, "cv37": 0xe0f8, "cv38": 0xe0fc,
  "cv39": 0xe100, "cv40": 0xe104, "cv41": 0xe108, "cv42": 0xe10c,
  "cv43": 0xe110, "cv44": 0xe114, "cv45": 0xe118, "cv46": 0xe11c,
  "cv47": 0xe120, "cv48": 0xe124, "cv49": 0xe128, "cv50": 0xe12c,
  "cv51": 0xe130, "cv52": 0xe134, "cv53": 0xe138, "cv54": 0xe13c,
  "cv55": 0xe140, "cv56": 0xe144, "cv57": 0xe148, "cv58": 0xe14c,
  "cv59": 0xe150, "cv60": 0xe154, "cv61": 0xe158, "cv62": 0xe15c,
  "cv63": 0xe160, "cv64": 0xe164, "cv65": 0xe168, "cv66": 0xe16c,
  "cv67": 0xe170, "cv68": 0xe174, "cv69": 0xe178, "cv70": 0xe17c,
  "cv71": 0xe180, "cv72": 0xe184, "cv73": 0xe188, "cv74": 0xe18c,
  "cv75": 0xe190, "cv76": 0xe194, "cv77": 0xe198, "cv78": 0xe19c,
  "cv79": 0xe1a0, "cv80": 0xe1a4, "cv81": 0xe1a8, "cv82": 0xe1ac,
  "cv83": 0xe1b0, "cv84": 0xe1b4, "cv85": 0xe1b8, "cv86": 0xe1bc,
  "cv87": 0xe1c0, "cv88": 0xe1c4, "cv89": 0xe1c8, "cv90": 0xe1cc,
  "cv91": 0xe1d0, "cv92": 0xe1d4, "cv93": 0xe1d8, "cv94": 0xe1dc,
  "cv95": 0xe1e0, "cv96": 0xe1e4, "cv97": 0xe1e8, "cv98": 0xe1ec,
  "cv99": 0xe1f0, "dist": 0xe1f4, "dlig": 0xe1f8, "dnom": 0xe1fc,
  "expt": 0xe200, "falt": 0xe204, "fin2": 0xe208, "fin3": 0xe20c,
  "fina": 0xe210, "frac": 0xe214, "fwid": 0xe218, "half": 0xe21c,
  "haln": 0xe220, "halt": 0xe224, "hist": 0xe228, "hkna": 0xe22c,
  "hlig": 0xe230, "hngl": 0xe234, "hojo": 0xe238, "hwid": 0xe23c,
  "init": 0xe240, "isol": 0xe244, "ital": 0xe248, "jalt": 0xe24c,
  "jp04": 0xe250, "jp78": 0xe254, "jp83": 0xe258, "jp90": 0xe25c,
  "kern": 0xe260, "lfbd": 0xe264, "liga": 0xe268, "ljmo": 0xe26c,
  "lnum": 0xe270, "locl": 0xe274, "ltra": 0xe278, "ltrm": 0xe27c,
  "mark": 0xe280, "med2": 0xe284, "medi": 0xe288, "mgrk": 0xe28c,
  "mkmk": 0xe290, "mset": 0xe294, "nalt": 0xe298, "nlck": 0xe29c,
  "nukt": 0xe2a0, "numr": 0xe2a4, "onum": 0xe2a8, "opbd": 0xe2ac,
  "ordn": 0xe2b0, "ornm": 0xe2b4, "palt": 0xe2b8, "pcap": 0xe2bc,
  "pkna": 0xe2c0, "pnum": 0xe2c4, "pref": 0xe2c8, "pres": 0xe2cc,
  "pstf": 0xe2d0, "psts": 0xe2d4, "pwid": 0xe2d8, "qwid": 0xe2dc,
  "rand": 0xe2e0, "rkrf": 0xe2e4, "rlig": 0xe2e8, "rphf": 0xe2ec,
  "rtbd": 0xe2f0, "rtla": 0xe2f4, "rtlm": 0xe2f8, "ruby": 0xe2fc,
  "salt": 0xe300, "sinf": 0xe304, "size": 0xe308, "smcp": 0xe30c,
  "smpl": 0xe310, "ss00": 0xe314, "ss01": 0xe318, "ss02": 0xe31c,
  "ss03": 0xe320, "ss04": 0xe324, "ss05": 0xe328, "ss06": 0xe32c,
  "ss07": 0xe330, "ss08": 0xe334, "ss09": 0xe338, "ss10": 0xe33c,
  "ss11": 0xe340, "ss12": 0xe344, "ss13": 0xe348, "ss14": 0xe34c,
  "ss15": 0xe350, "ss16": 0xe354, "ss17": 0xe358, "ss18": 0xe35c,
  "ss19": 0xe360, "ss20": 0xe364, "ss21": 0xe368, "ss22": 0xe36c,
  "ss23": 0xe370, "ss24": 0xe374, "ss25": 0xe378, "ss26": 0xe37c,
  "ss27": 0xe380, "ss28": 0xe384, "ss29": 0xe388, "ss30": 0xe38c,
  "ss31": 0xe390, "ss32": 0xe394, "ss33": 0xe398, "ss34": 0xe39c,
  "ss35": 0xe3a0, "ss36": 0xe3a4, "ss37": 0xe3a8, "ss38": 0xe3ac,
  "ss39": 0xe3b0, "ss40": 0xe3b4, "ss41": 0xe3b8, "ss42": 0xe3bc,
  "ss43": 0xe3c0, "ss44": 0xe3c4, "ss45": 0xe3c8, "ss46": 0xe3cc,
  "ss47": 0xe3d0, "ss48": 0xe3d4, "ss49": 0xe3d8, "ss50": 0xe3dc,
  "ss51": 0xe3e0, "ss52": 0xe3e4, "ss53": 0xe3e8, "ss54": 0xe3ec,
  "ss55": 0xe3f0, "ss56": 0xe3f4, "ss57": 0xe3f8, "ss58": 0xe3fc,
  "ss59": 0xe400, "ss60": 0xe404, "ss61": 0xe408, "ss62": 0xe40c,
  "ss63": 0xe410, "ss64": 0xe414, "ss65": 0xe418, "ss66": 0xe41c,
  "ss67": 0xe420, "ss68": 0xe424, "ss69": 0xe428, "ss70": 0xe42c,
  "ss71": 0xe430, "ss72": 0xe434, "ss73": 0xe438, "ss74": 0xe43c,
  "ss75": 0xe440, "ss76": 0xe444, "ss77": 0xe448, "ss78": 0xe44c,
  "ss79": 0xe450, "ss80": 0xe454, "ss81": 0xe458, "ss82": 0xe45c,
  "ss83": 0xe460, "ss84": 0xe464, "ss85": 0xe468, "ss86": 0xe46c,
  "ss87": 0xe470, "ss88": 0xe474, "ss89": 0xe478, "ss90": 0xe47c,
  "ss91": 0xe480, "ss92": 0xe484, "ss93": 0xe488, "ss94": 0xe48c,
  "ss95": 0xe490, "ss96": 0xe494, "ss97": 0xe498, "ss98": 0xe49c,
  "ss99": 0xe4a0, "subs": 0xe4a4, "sups": 0xe4a8, "swsh": 0xe4ac,
  "titl": 0xe4b0, "tjmo": 0xe4b4, "tnam": 0xe4b8, "tnum": 0xe4bc,
  "trad": 0xe4c0, "twid": 0xe4c4, "unic": 0xe4c8, "valt": 0xe4cc,
  "vatu": 0xe4d0, "vert": 0xe4d4, "vhal": 0xe4d8, "vjmo": 0xe4dc,
  "vkna": 0xe4e0, "vkrn": 0xe4e4, "vpal": 0xe4e8, "vrt2": 0xe4ec,
  "zero": 0xe4f0
};
