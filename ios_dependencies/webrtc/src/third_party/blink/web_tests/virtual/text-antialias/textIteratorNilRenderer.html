<html>
<head>
<style>
    #qt .addr{display:none}
</style>
</head>

<body>
<center>

<form name=f action="/froogle">

<table cellpadding=0 cellspacing=0 id=qt class=hide-addr>
<tr>
<td width=150 rowspan=2>&nbsp;
<td>
<input maxlength=256 name=q id=q size=40 value="">
<td class=addr>
<input type=text name=addr id=addr value="" disabled>

<td>
<input name=btnG type=submit id=submit_button value="Search Froogle">
<td width=150 rowspan=2 style="font-size:x-small;padding-left:1em;">
<input type=hidden name=sl id=sl value=off disabled>
<label>
<input type=checkbox onclick="this.form.elements.sl.value=this.checked?'on':'off'">
Remember this location
</label>
</table>
</form>
</table>
<br>
</center>

<script>
        if (window.eventSender) {
            eventSender.mouseMoveTo(250,20);
            eventSender.mouseDown();
            eventSender.mouseUp();
            eventSender.mouseMoveTo(150, 20);
            eventSender.mouseDown();
            eventSender.mouseUp();
            eventSender.mouseMoveTo(250,20);
            eventSender.mouseDown();
            eventSender.mouseUp();
            eventSender.mouseMoveTo(150, 20);
            eventSender.mouseDown();
            eventSender.mouseUp();
            eventSender.mouseMoveTo(250,20);
            eventSender.mouseDown();
            eventSender.mouseUp();
            eventSender.mouseMoveTo(150, 20);
            eventSender.mouseDown();
            eventSender.mouseUp();
            eventSender.mouseMoveTo(250,20);
            eventSender.mouseDown();
            eventSender.mouseUp();
            eventSender.mouseMoveTo(150, 20);
            eventSender.mouseDown();
            eventSender.mouseUp();
       }
</script>

</body>
</html>
