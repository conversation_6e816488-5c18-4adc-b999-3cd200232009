This test confirms fractional values for word- and letter-spacing are not truncated in complex path.

On success, you will see a series of "PASS" messages, followed by "TEST COMPLETE".

PASS getWidth(ls005)-getWidth(ls0) is within 1 of 73
PASS getWidth(ws005)-getWidth(ws0) is within 1 of 5
PASS successfullyParsed is true

TEST COMPLETE


For each pair of paragraphs, the second one should be wider than the first one.

This test ensures that we properly accumulate sub-pixel word and letter spacing: bug 336358.
This test ensures that we properly accumulate sub-pixel word and letter spacing: bug 336358.
Pack <PERSON>'s box with five dozen liquor jugs!
Pack <PERSON>'s box with five dozen liquor jugs!
