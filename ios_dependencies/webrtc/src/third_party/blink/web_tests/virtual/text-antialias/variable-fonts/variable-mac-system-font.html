<!DOCTYPE html>
<meta charset="utf-8">
<script src="../../../resources/testharness.js"></script>
<script src="../../../resources/testharnessreport.js"></script>
<style>
.variation {
font-family: Skia, sans-serif;
font-size: 150px;
}

#narrow {
font-variation-settings: "wdth" 0.61998;
}

#wide {
font-variation-settings: "wdth" 1.29999;
}
</style>
<span class="variation" id="narrow">wwwwide</span><br>
<span class="variation" id="wide">wwwwide</span>
<script>
setup({ explicit_done: true });
test(function() { assert_true(narrow.getBoundingClientRect().width <
                              wide.getBoundingClientRect().width * 0.6); },
     "Narrow Skia variation is less than 60% as wide.");
done();
</script>
