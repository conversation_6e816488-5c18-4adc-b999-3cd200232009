<style>
.ahem {
    font:20px/1 Ahem;
}
</style>
<p>
This page ensures that WebKit can render unicode variation selector correctly.  On platforms which support UVSes, the glyph of U+0061 should be a 0.2em high, 1em wide rectangle. On platforms which don't support UVSes, it should be a square.  In addition, any glyphs (including the last resort glyph) should not appear after the glyph on all platforms.
</p>
<div>
Without UVS:
<span class="ahem">X</span>
should look like <span class="ahem">X</span>
</div>
<div>
With UVS:
<span class="ahem">p</span>
should look like <span class="ahem">p</span>
</div>
<div>
UVS not in the font should fallback to base:
<span class="ahem">X</span>
should look like <span class="ahem">X</span>
</div>
