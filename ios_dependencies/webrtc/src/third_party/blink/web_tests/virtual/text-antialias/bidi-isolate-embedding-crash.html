<script>
if (window.testRunner)
    testRunner.dumpAsText();
</script>
<!-- In debug builds, this would ASSE<PERSON> while trying to find the first bidi-participating
object on the line.  We'd scan, find the explicit LTR embed char implied by the
outer span, add that to the explicit-embeding stack, then walk into the isolate
and find our first bidi object on the line (the PASS... text node).  But, right before
returning that we'd try to commit the open embedings, and ASSERT, as the PASS is
inside the isolate and is not affected by the outer embeddings.
https://bugs.webkit.org/show_bug.cgi?id=76574 -->
<span style="unicode-bidi: embed;"><bdi dir="rtl">PASS if did not crash.</bdi></span>
