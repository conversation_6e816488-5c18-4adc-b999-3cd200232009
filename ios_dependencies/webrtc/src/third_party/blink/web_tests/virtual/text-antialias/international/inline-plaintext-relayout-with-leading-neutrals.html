<!DOCTYPE HTML>
<html><head>
<meta http-equiv="Content-Type" content="text/html;charset=utf-8">
<style>
  .plaintext {
    unicode-bidi: -webkit-plaintext;
    unicode-bidi: plaintext;
  }
  .border {
    border: solid thin gray;
    width: 20px;
  }
</style>
<script>
function runTest() {
  var span1 = document.getElementById("replacement1");
  span1.innerText = "א";
  var span2 = document.getElementById("replacement2");
  span2.innerText = "a";
}
</script>
</head><body onload="runTest();">
  <table>
    <tr>
      <td class="border"><span class="plaintext">1... .... ....<span id="replacement1">a</span></span></td>
      <td>should look the same as</td>
      <td class="border"><span dir="rtl">1... .... ....<span>&#x05D0;</span></span></td>
    </tr>
    <tr>
      <td class="border"><span class="plaintext">2... .... ....<span id="replacement2">&#x05D0;</span></span></td>
      <td>should look the same as</td>
      <td class="border"><span dir="ltr">2... .... ....<span>a</span></span></td>
    </tr>
  </table>
</body></html>
