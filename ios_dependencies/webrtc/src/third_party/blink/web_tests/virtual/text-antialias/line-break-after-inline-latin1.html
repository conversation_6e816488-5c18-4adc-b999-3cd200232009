<!DOCTYPE html>
<html>
<head>
<title>Line breaks after span inlines - Latin1</title>
<script src="../../resources/testharness.js"></script>
<script src="../../resources/testharnessreport.js"></script>
</head>
<body>
<meta charset=utf-8>
<div>
<p>With span inline on first word, Latin1 in second position of second word.</p>
<p style="font-family:courier; font-size:15px; width: 25px"><span>xx</span> xéx</p>
<p>With span inline on first word, Latin1 in first position of second word.</p>
<p style="font-family:courier; font-size:15px; width: 25px"><span>xx</span> éxx</p>
<p>With span inline on first word, no Latin1 in second word.</p>
<p style="font-family:courier; font-size:15px; width: 25px"><span>xx</span> xxx</p>
<p>Without span, Latin1 in second position of second word.</p>
<p style="font-family:courier; font-size:15px; width: 25px">xx xéx</p>
<p>Without span, Latin1 in first position of second word.</p>
<p style="font-family:courier; font-size:15px; width: 25px">xx éxx</p>
<p>Without span, no Latin1 in second word.</p>
<p style="font-family:courier; font-size:15px; width: 25px">xx xxx</p>
</div>
<div><pre id=results></pre></div>
<script>
function mergeRect(rects, rect) {
    var newRects = [];
    if (!rects.length)
        newRects.push(rect);
    else {
        for (var i = 0; i < rects.length; ++i) {
            var r = rects[i];
            if (!rect)
                newRects.push(r);
            else if (rect.top < r.top) {
                newRects.push(rect);
                newRects.push(r);
                rect = null;
            } else if (rect.top == r.top) {
                if (rect.left + rect.width > r.left + r.width)
                    r.width = rect.left + rect.width - r.left;
                newRects.push(r);
                rect = null;
            } else
                newRects.push(r);
        }
        if (rect)
            newRects.push(rect);
    }
    return newRects;
}

function mergeRects(rects, newRects) {
    for (var i = 0; i < newRects.length; ++i)
        rects = mergeRect(rects, newRects[i]);
    return rects;
}

function getLineRects(paragraphNumber) {
    var range = document.createRange();
    var paragraphs = document.getElementsByTagName("p");
    var p = paragraphs[paragraphNumber];
    var rects = [];
    for (var i = 0; i < p.childNodes.length; ++i) {
        range.setStart(p, i);
        range.setEnd(p, i + 1);                     
        rects = mergeRects(rects, range.getClientRects());
    }
    return rects;
}

function compareParagraphLineRects(paragraphNumber1, paragraphNumber2) {
    var rects1 = getLineRects(paragraphNumber1);
    var rects2 = getLineRects(paragraphNumber2);
    assert_equals(rects1.length, rects2.length,
      'FAIL: different number of lines, got ' + rects1.length + ', expected ' + rects2.length);

    for (var i = 0; i < rects1.length; ++i) {
      assert_equals(rects2[i].width, rects1[i].width,
        'FAIL: para[' + paragraphNumber1 + '] line[' + i + '] width differs from para[' + paragraphNumber2 + '] line[' + i + ']' +
        ', got ' + rects1[i].width + ', expected ' + rects2[i].width);
    }
}

test(() => {
  for (var i = 0; i < 3; i++ )
    compareParagraphLineRects(i*2 + 1, i*2 + 1 + 6);
});
</script>
</body>
</html>
