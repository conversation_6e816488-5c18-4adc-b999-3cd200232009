<head>
<script>

function testString(s)
{
    var fast = document.getElementById("fastPath");
    fast.firstChild.data = s;
    var fastWidth = fast.getBoundingClientRect().width;
    var slow = document.getElementById("slowPath");
    slow.firstChild.data = s;
    var slowWidth = slow.getBoundingClientRect().width;
    return fastWidth == slowWidth;
}

function test()
{
    if (window.testRunner)
        testRunner.dumpAsText();

    var testStrings = ["\u02E5\u02E9", "\u02E9\u02E5", "\u02E7\u02E9",
                       "\u02E5\u02E7", "\u02E5\u02E7\u02E5"];
                    
    var failedStrings = new Array;
    for (var i = 0; i < testStrings.length; ++i) 
        if (!testString(testStrings[i]))
            failedStrings.push(testStrings[i]);

    var testArea = document.getElementById("testArea");
    testArea.parentNode.removeChild(testArea);

    if (failedStrings.length > 0) 
        result = "FAIL: The following strings are rendered differently depending on what text-rendering is set to : " + failedStrings.join(", ");
    else
        result = "PASS: All strings have identical widths regardless of what text-rendering is set to.";
    document.getElementById("result").firstChild.data = result;
}
</script>
</head>
<body onload="test()">
  <p>This test checks if sequences of IPA tone marks are treated as complex script characters and ligated when a font capable of that is specified regardless of text-rendering mode. It's for <a href="http://bugs.webkit.org/show_bug.cgi?id=39799">bug 39799</a></p>
<p id="result">FAIL: Script did not run to completion.</p>
<p id="testArea" style="font-family: Arial;">
<span id="fastPath" style="text-rendering: optimizeSpeed"> </span>
<span id="slowPath" style="text-rendering: optimizeLegibility"> </span>
</p>
</body>
