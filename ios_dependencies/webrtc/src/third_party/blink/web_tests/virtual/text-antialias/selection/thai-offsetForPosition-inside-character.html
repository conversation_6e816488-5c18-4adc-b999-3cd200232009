<p>
    To test manually, verify that you can select each one of the Thai glyphs
    separately from the other by dragging.
</p>
<p id="result">
</p>
<div id="target" style="font-size: 48px; float: left;">&#3652;&#3614;</div>
<script>
    if (window.testRunner) {
        testRunner.dumpAsText();

        var result = document.getElementById("result");
        result.innerText = "FAIL: Test did not finish";

        var target = document.getElementById("target");
        var x = target.offsetLeft + target.offsetWidth / 2;
        var y = target.offsetTop + target.offsetHeight / 2;

        eventSender.mouseMoveTo(x, y);
        eventSender.mouseDown();
        eventSender.mouseUp();

        var selection = getSelection();
        if (selection.baseNode === target.firstChild && selection.baseOffset === 1)
            result.innerText = "PASS";
        else
            result.innerText = "FAIL: Clicking in the middle put the insertion point at " + selection.baseNode + "[" + selection.baseOffset + "]";
    }
</script>
