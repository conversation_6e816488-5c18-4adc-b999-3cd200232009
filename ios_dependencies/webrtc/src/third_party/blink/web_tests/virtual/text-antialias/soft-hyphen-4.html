<html>
<head>
    <script src="../../resources/ahem.js"></script>
    <style>
        .testbox   {
            border: solid;
            padding: 4px;
            width: 330px;
            font: 18px Ahem;
            text-align: justify;
            -webkit-font-smoothing: none;
        }
    </style>
</head>
<body>
    <p>
        <!--In this test, the top-right boxes should be the same length-->
        <div class="testbox">
            12345678901 anti&shy;aliasing<br>    <!--broken (no space after aliasing)-->
            <br>
            12345678901 anti&shy;aliasing         <!--works (space after aliasing)-->
        </div>
    </p>
</body>
</html>
