<!DOCTYPE html>
<html>
    <head>
        <title>Tests handling of unmatched surrogate pairs</title>
        <meta charset="utf16-be">
    </head>
    <body>
        <script>
            function appendLine(str)
            {
                var line = document.createElement('div');
                line.appendChild(document.createTextNode(str));
                document.body.appendChild(line);
            }

            var globe = '\u{1F30E}';
            appendLine('Full codepoint, "\u{1F30E}". Prints a globe glyph.');
            appendLine('First part of surrogate pair, "' + globe.substr(0, 1) +
                '". Should print replacement character and rest of run.');
            appendLine('Second part of surrogate pair, "' + globe.substr(1, 1) +
                '". Should print replacement character and rest of run.');
            document.body.appendChild(document.createElement('br'));

            appendLine('Spanning text nodes:');
            var nodeA = document.createTextNode('- First part "' +
                globe.substr(0, 1));
            var nodeB = document.createTextNode(globe.substr(1, 1) +
                '" second part.');
            var container = document.createElement('div');
            container.appendChild(nodeA);
            container.appendChild(nodeB);
            // Rendering of unmatched surrogate pair is not interoperable.
            // document.body.appendChild(container);

            appendLine('After element.normalize():');
            var clone = container.cloneNode(true);
            clone.normalize();
            document.body.appendChild(clone);
        </script>
    </body>
</html>
