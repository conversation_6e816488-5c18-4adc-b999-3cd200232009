<!DOCTYPE html>
<html>
<body>

<!-- To calculate width of composed glyph -->
<div style="font-size:500%">
<span id="reference">&#x5e9;&#x5b0;</span>
</div>

<p>U+05e9 U+05b0 of following text should be selected.</p>
<div style="font-size:500%">
<span id="target">&#x5e1;&#x5b0;&#x5e9;&#x5b0;</span>
</div>

<script>
var width = document.getElementById("reference").offsetWidth;
var target = document.getElementById("target");

if (window.eventSender) {
    eventSender.mouseMoveTo(target.offsetLeft + 5, target.offsetTop + 5);
    eventSender.mouseDown();
    eventSender.mouseMoveTo(target.offsetLeft + 5 + width / 2, target.offsetTop + 5);
    eventSender.mouseUp();
}
</script>
</body>
</html>
