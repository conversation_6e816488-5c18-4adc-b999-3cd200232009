<html>
<head>
<title></title>
</head>
<body style="overflow: hidden">
<p>
This tests for <i>http://bugzilla.opendarwin.org/show_bug.cgi?id=6014
Bidi algorithm: incorrect resolved levels for neutrals between R and ET ON
L</i>.
</p>
<p>
The characters &#x05d0; and &#x05d1; are of type R; ^ and @
are of type ON; $ and % are of type ET; a is of type L; 1 is of type EN. In
each of the following pairs, the two lines should be identical to each other.
</p>
<hr>
<p style="direction: ltr; text-align: left;">&#x05d0; ^ @ $% a</p>
<bdo dir="ltr">&#x05d0; ^ @ $% a</bdo>
<hr>
<p style="direction: ltr; text-align: left;">&#x05d0; ^ @ $% &#x05d1;</p>
<bdo dir="ltr">&#x05d1; %$ @ ^ &#x05d0;</bdo>
<hr>
<p style="direction: ltr; text-align: left;">&#x05d0; ^ @ $%1</p>
<bdo dir="ltr">$%1 @ ^ &#x05d0;</bdo>
<hr>
<p style="direction: rtl; text-align: left;">&#x05d0; ^ @ $% a</p>
<bdo dir="ltr">a %$ @ ^ &#x05d0;</bdo>
<hr>
<p style="direction: rtl; text-align: left;">&#x05d0; ^ @ $% &#x05d1;</p>
<bdo dir="ltr">&#x05d1; %$ @ ^ &#x05d0;</bdo>
<hr>
<p style="direction: rtl; text-align: left;">&#x05d0; ^ @ $%1</p>
<bdo dir="ltr">$%1 @ ^ &#x05d0;</bdo>
</body>
</html>
