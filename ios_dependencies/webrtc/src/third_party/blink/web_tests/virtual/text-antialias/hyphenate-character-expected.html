<style>
    .text-box {
        border: solid;
        padding: 4px;
        width: 110px;
        font-size: 18px;
        text-align: justify;
    }

    .hyphenate-character-tilde-dot {
        -webkit-hyphenate-character: "\B7~";
    }

    .test {
        float: left;
        margin: 0 4px;
    }
</style>
<div class="test">
    <p>The initial value (should be like <tt>auto</tt>)</p>
    <div class="text-box">
      antidisestab-lishment-arianism
    </div>
</div>
<div class="test">
    <p>The default (<tt>hyphenate-character: auto</tt>)</p>
    <div class="text-box hyphenate-character-auto">
      antidisestab-lishment-arianism
    </div>
</div>
<div class="test">
    <p>Bullet (<tt>hyphenate-character: "\2022"</tt>)</p>
    <div class="text-box hyphenate-character-bullet">
      antidisestab&#x2022; lishment&#x2022; arianism
    </div>
</div>
<div class="test">
    <p>Middle dot and tilde (<tt>hyphenate-character: "\B7~"</tt>)</p>
    <div class="text-box hyphenate-character-tilde-dot">
      antidisestab&#xb7;~ lishment&#xb7;~ arianism
    </div>
</div>
