<html> 
<head>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8" /> 

<style>
.editing { 
    border: 2px solid red; 
    padding: 12px; 
    font-size: 24px; 
}
</style>

<script>
function log(str) {
    var li = document.createElement("li");
    li.appendChild(document.createTextNode(str));
    var console = document.getElementById("console");
    console.appendChild(li);
}

function convertStringToUnicode(string)
{
    var returnValue = " (character in Unicode value): ";
    for (var i = 0; i < string.length; ++i)
    {
        returnValue += " " + string.charCodeAt(i);
    }
    return returnValue;
}

function assertEqual(test_name, actual, expected)
{
    if (actual != expected) {
        log("==================================");
        log("FAILED: " + test_name);
        var actual_string = "actual" + convertStringToUnicode(actual);
        var expected_string = "expected" + convertStringToUnicode(expected);
        log(actual_string);
        log(expected_string);
    }
}

onload = function()
{
    if (window.testRunner)
        testRunner.dumpAsText();

    var div = document.getElementById("div");
    var string = div.innerHTML;
    assertEqual("arabic string", string, "\u0641\u0642 \u202A\u0643 \u0644 abc \u202C \u0645");

    var sel = getSelection();
    sel.collapse(div, 0);
    
    var positions = [];
    while (true) {
        positions.push({ node: sel.extentNode, begin: sel.baseOffset, end: sel.extentOffset });
        sel.modify("move", "right", "character");
        if (positions[positions.length - 1].node == sel.extentNode && positions[positions.length - 1].end == sel.extentOffset)
            break;
    };

    for (var i = 0; i < positions.length; ++i)
        log("(" + positions[i].begin + "," + positions[i].end + ")");
}
</script>

<title>Editing Test</title> 
</head> 
<body>
<div contenteditable id="div" class="editing">&#x0641;&#x0642; &#x202a;&#x0643; &#x0644; abc &#x202c; &#x0645;</div>
<ul id="console"></ul>
</body>
</html>
