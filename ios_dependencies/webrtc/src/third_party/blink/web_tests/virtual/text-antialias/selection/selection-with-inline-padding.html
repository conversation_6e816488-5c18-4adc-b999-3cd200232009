<!DOCTYPE html>
<style>
p > b {
  padding: 10px;
}
</style>
<p>
  Lorem ipsum dolor sit amet, consectetur adipiscing elit.<br>
  <b id="test">Sed dictum erat sit amet pharetra pretium.</b><br>
  <PERSON><PERSON><PERSON> a est vitae orci tempus tincidunt nec at dolor.
</p>
<p>
  Tests whether selection includes padding in the Y direction for inline
  elements. This padding would ideally not be included. http://crbug.com/657325#c13
</p>
<script src="../../../resources/run-after-layout-and-paint.js"></script>
<script>
runAfterLayoutAndPaint(function() {
  var node = document.getElementById('test').firstChild;
  var range = document.createRange();
  range.setStart(node, 5);
  range.setEnd(node, node.length - 5);
  window.getSelection().addRange(range);
}, true);
</script>
