<!DOCTYPE html>
<style>
div {
    text-rendering: optimizeLegibility;
    white-space:nowrap;
}
</style>
<script src="../../resources/js-test.js"></script>
<p>For each pair of paragraphs, the second one should be wider than the first one.</p>
<div id="ls0">
This test ensures that we properly accumulate sub-pixel word and letter spacing: <a href="http://crbug.com/336358">bug 336358</a>.
</div>
<div id="ls005" style="letter-spacing: 0.05em">
This test ensures that we properly accumulate sub-pixel word and letter spacing: <a href="http://crbug.com/336358">bug 336358</a>.
</div>
<div id="ws0">
Pack <PERSON>'s box with five dozen <a href="#pangram">liquor jugs</a>!
</div>
<div id="ws005" style="word-spacing: 0.05em">
Pack <PERSON>'s box with five dozen <a href="#pangram">liquor jugs</a>!
</div>
<script>
description("This test confirms fractional values for word- and letter-spacing are not truncated in complex path.");

function getWidth(element) {
    if (window.getComputedStyle(element).display != 'block')
        return elment.offsetWidth;
    var child = element.firstChild;
    if (!child)
        return 0;
    if (child === element.lastChild)
        return getWidth(child);
    var span = document.createElement('span');
    while (child = element.firstChild)
        span.appendChild(child);
    element.appendChild(span);
    return span.offsetWidth;
}

shouldBeCloseTo('getWidth(ls005)-getWidth(ls0)', 73, 1);
shouldBeCloseTo('getWidth(ws005)-getWidth(ws0)', 5, 1); // 7*0.05em = 5.6px.
</script>
