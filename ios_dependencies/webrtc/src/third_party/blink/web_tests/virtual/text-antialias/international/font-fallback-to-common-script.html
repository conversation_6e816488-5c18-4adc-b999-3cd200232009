<!DOCTYPE html>
<html>
<head>
<script>
if (window.internals) {
    internals.settings.setStandardFontFamily("Times", "<PERSON>");
    internals.settings.setStandardFontFamily("", "<PERSON>");
    internals.settings.setStandardFontFamily("<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>");

    internals.settings.setSansSerifFontFamily("<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>");
}
</script>
</head>
<body>
<!-- Test for font fallback to the common script when the per-script font setting is missing or the empty string.
Bug 81329 <https://bugs.webkit.org/show_bug.cgi?id=81329> -->
<div style="font-size: 20px">
<div lang="zh-CN">this is ahem font</div>

<!-- Test fallback to common script when no setting is set. This assumes there is no setting for "Zxxx" (script code for unwritten languages). -->
<div lang="und-Zxxx" style="font-family: sans-serif;">this is ahem font</div>
</div>
</body>
</html>
