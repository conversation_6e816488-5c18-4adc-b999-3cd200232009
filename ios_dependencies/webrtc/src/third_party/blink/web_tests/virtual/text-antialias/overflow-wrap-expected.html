<!DOCTYPE html>
<html>
<head>
    <title>CSS Test: 'overflow-wrap' property is identical to 'text-wrap' property.</title>
    <link rel="author" title="<PERSON>" href="mailto:<EMAIL>" />
    <link rel="help" href="http://www.w3.org/TR/2012/WD-css3-text-20120814/#overflow-wrap" />
    <script type="text/javascript">
        function test()
        {
            var propertyName = "word-wrap";

            var value = window.getComputedStyle(document.getElementById("default"), null).getPropertyValue(propertyName);
            var text = "Wrapping is : " + value + ".\n";

            document.getElementById("default-value").textContent = text;

            value = window.getComputedStyle(document.getElementById("break-word"), null).getPropertyValue(propertyName);
            text = "Wrapping is : " + value + ".\n";

            document.getElementById("break-word-value").textContent = text;
        }
    </script>
</head>
<body onload="test()">
    <div id="default" style="border:2px solid blue;width:150px;">Longlonglonglonglonglonglonglonglongword</div>
    <p id="default-value" style="white-space: pre"></p>
    <div id="break-word" style="border:2px solid blue;width:150px;word-wrap:break-word">
        Longlonglonglonglonglonglonglonglongword
        <p>Longlonglonglonglonglonglonglonglongword</p>
    </div>
    <p id="break-word-value" style="white-space: pre"></p>
</body>
</html>
