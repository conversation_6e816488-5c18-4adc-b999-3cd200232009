<!DOCTYPE html>
<script>
if (window.internals)
  internals.setMockHyphenation('en-us');
</script>
<style>
dt {
  float: left;
  width: 19ch;
}
dd {
  margin-left: 20ch;
}
div {
  border: thick solid blue;
  width: 8ch;
  font-family: 'Courier New';
}
.ahem {
  font-family: ahem;
}
.hyphens {
  hyphens: auto;
  -webkit-hyphens: auto;
  -moz-hyphens: auto;
  -ms-hyphens: auto;
}
.break-word {
  word-wrap: break-word;
}
.break-all {
  word-break: break-all;
}
</style>
<body lang="en-us">
<dl>
  <dt>hyphens
  <dd><div class="hyphens">hyphenation is</div>
  <dd><div class="hyphens">hyphenation</div>
  <dt>hyphens break-word
  <dd><div class="hyphens break-word">hyphenation is</div>
  <dd><div class="hyphens break-word">hyphenation</div>
  <dt>hyphens break-all
  <dd><div class="hyphens break-all">hyphenation is</div>
  <dd><div class="hyphens break-all">hyphenation</div>
  <dt>hyphens break-all break-word
  <dd><div class="hyphens break-all break-word">hyphenation is</div>
  <dd><div class="hyphens break-all break-word">hyphenation</div>
  <dt>break-word
  <dd><div class="ahem break-word">XXXXXXXX)</div>
  <dt>break-all
  <dd><div class="ahem break-all">XXXXXXXX)</div>
  <dt>break-all break-word
  <dd><div class="ahem break-all break-word">XXXXXXXX)</div>
</dl>
</body>
