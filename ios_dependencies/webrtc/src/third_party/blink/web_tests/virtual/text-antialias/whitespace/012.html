<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.0//EN">
<html>
 <head>
  <title>white-space: mixed tests (simple)</title>
  <script src="../../../resources/ahem.js"></script>
  <style type="text/css">
   .control { display: inline; font: 1em/1 Ahem, sans-serif; background: red; color: white; }
   .test { font: 1em/1 Ahem; background: green; color: lime; float: left; clear: left; margin: 1em; }
   .normal { white-space: normal; }
   .nowrap { white-space: nowrap; }
   .pre { white-space: pre; }
  </style>
 </head>
 <body>
  <div class="control">Ahem_font_required_for_this_test.</div>
  <p>The following two blocks should be identical.</p>
  <div class="test normal">[<span class="normal"> </span><span class="pre"> </span><span class="normal"> </span><span class="pre"> </span><span class="normal"> </span>]</div>
  <div class="test pre">[     ]</div>
 </body>
</html>
