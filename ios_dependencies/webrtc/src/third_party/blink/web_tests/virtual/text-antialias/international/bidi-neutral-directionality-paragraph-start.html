<html>
<head>
<meta http-equiv="content-type" content="text/html; charset=UTF-8">
</head>
<body>
<p><PERSON><PERSON> contributed this fix to KDE, and now we're rolling it in. Here's his explanation:</p>
<p>The directionality of a neutral character at the beginning of a paragraph (or after a hard line break) is decided incorrectly if the first non-neutral character in the paragraph has directionality opposite to the paragraph directionality. For example, if the paragraph direction is LTR, the first character on the paragraph is a question mark and the next one is a Hebrew character, then the question mark will is considered right-to-left and appears to the right of the Hebrew character.</p>
<p>The rule to follow is 3.3.4.N2 in the Unicode Standard's Bidirectional Algorithm <a href="http://www.unicode.org/reports/tr9/#Resolving_Neutral_Types">http://www.unicode.org/reports/tr9/#Resolving_Neutral_Types</a>.</p>
<p>If the test is successful, the question marks should be on the far left and far right of the next two paragraphs.</p>
<p dir="ltr"> 
? ﺲ ﻭhello 
</p>
<p dir="rtl"> 
? hello ﺲ ﻭ 
</p>
</body>
</html>
