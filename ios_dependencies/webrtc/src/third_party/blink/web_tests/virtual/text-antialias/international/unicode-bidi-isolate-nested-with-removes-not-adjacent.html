<!doctype html>
<!-- This tests for regression of https://crbug.com/279277 where non-adjacent, nested isolates caused a use-after-free if the elements were later removed. -->
<script>
window.onload = function() {
  document.body.offsetTop;
  b.lastChild.parentNode.removeChild(b.lastChild);
  document.body.offsetTop;
  a.nextSibling.parentNode.removeChild(a.nextSibling);
  document.body.offsetTop;

  document.write("PASS did not crash");
}
</script>

<body>
  <div id="a">foo</div><div>baz</div><div></div>
  <div>
    <output>
      <span>
        <output>bar</output>
        <span id="b">
          <span>
            <div style="display:inline-block"></div>
            <br><br>
          </span>
        </span>
      </span>
    </output>
  </div>
</body>

<script>
if (window.testRunner)
    testRunner.dumpAsText();
</script>
