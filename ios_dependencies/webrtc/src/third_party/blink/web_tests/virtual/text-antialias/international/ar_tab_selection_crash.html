<!DOCTYPE html>
<html>
    <head>
        <meta charset="utf-8">
        <title>Test handling of selection with arabic and tab characters</title>
    </head>
    <body>
        <pre>א		1</pre>
        <p>
            Test passes if it does not crash.
        </p>
        <script>
            if (window.testRunner) {
                testRunner.dumpAsText();
            }

            var testElement = document.getElementsByTagName('pre')[0];
            var range = document.createRange();
            range.setStart(testElement.firstChild, 1);
            range.setEnd(testElement.firstChild, 3);
            window.getSelection().addRange(range);

            if (window.eventSender) {
                var elementBounds = testElement.getBoundingClientRect();
                var rangeBounds = range.getBoundingClientRect();
                var x = (elementBounds.right - rangeBounds.right) / 2;
                var y = (elementBounds.bottom - elementBounds.top) / 2;
                eventSender.mouseMoveTo(0, 0);
                eventSender.mouseMoveTo(x, y);
                eventSender.mouseDown();
                eventSender.mouseUp();
            }
        </script>
    </body>
</html>
