<!DOCTYPE html>
<meta charset='utf-8'>
<style>
div {
    width: 95px;
    font: 12px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}
.rtl {
  direction: rtl;
}
span {
    display: inline-block;
}
</style>
<p>crbug.com/133700: Ellipsis placed correctly in rtl text in an inline-block wth an rtl flow.</p>

<p>You should see abcdef... below.</p>
<div>
      <span>abc</span> <span>def</span> <span>ghi</span> <span>abc</span> <span>def</span> <span>ghi</span>
</div>

<p>You should see ...defabc below.</p>
<div class="rtl">
      <span>abc</span> <span>def</span> <span>ghi</span> <span>abc</span> <span>def</span> <span>ghi</span>
</div>

<p>You should see abcde... then abcde... below.</p>
<div>
      <span>abcdefghijklm<br>abcdefghijklm</span> <span>ghi</span> <span>jkl</span> <span>ghi</span> <span>jkl</span>
</div>

<p>You should see ...hijklm then ...hijklm below.</p>
<div class="rtl">
      <span>abcdefghijklm<br>abcdefghijklm</span> <span>ghi</span> <span>jkl</span> <span>ghi</span> <span>jkl</span>
</div>
