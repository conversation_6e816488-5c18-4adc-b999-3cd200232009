<!DOCTYPE html>
<html>
<head>
<meta http-equiv="Content-Type" content="text/html;charset=utf-8">
<title>Letter spacing and Ligature</title>

<style type="text/css">
@font-face {
  font-family: megalopolis;
  src: url(../../third_party/MEgalopolis/MEgalopolisExtra.woff) format("woff");
}

.dligDiv {
-moz-font-feature-settings:"frac" 1, "dlig" 1;
-moz-font-feature-settings:"frac=1, dlig=1";
-ms-font-feature-settings:"frac" 1, "dlig" 1;
-o-font-feature-settings:"frac" 1, "dlig" 1;
-webkit-font-feature-settings:"frac" 1, "dlig" 1;
font-feature-settings:"frac" 1, "dlig" 1;
}

.fontVariantDiv {
font-variant-ligatures: discretionary-ligatures;
}

.onumDiv {
-moz-font-feature-settings: "onum" 1;
-ms-font-feature-settings: "onum" 1;
-o-font-feature-settings: "onum" 1;
-webkit-font-feature-settings: "onum" 1;
font-feature-settings: "onum" 1;
}

.common {
font-size: 24px;
line-height: 100%;
padding: 0px;
letter-spacing:20px;
font-family: megalopolis;
}

.noLetterSpace {
letter-spacing: 0;
}

p { font-family: serif; font-style: italic; }
</style>

<script src="../../resources/testharness.js"></script>
<script src="../../resources/testharnessreport.js"></script>
<script>
    setup({ explicit_done: true });
    function testLetterSpaceAndFeatureLigature() {
        var elementWidthWithFeatureLigatureAndLetterSpacing = dligSpan.getBoundingClientRect().width;
        var elementWidthWithLetterSpacing = letterSpace.getBoundingClientRect().width;
        test(function() {
            assert_not_equals(elementWidthWithFeatureLigatureAndLetterSpacing, elementWidthWithLetterSpacing, "Ligature applied despite letter spacing.");
            }, "Ligature expected to be applied despite letter spacing.");
    }

   function testLetterSpaceAndVariantLigature() {
        var elementWidthWithVariantLigatureAndLetterSpacing = fontVariantSpan.getBoundingClientRect().width;
        var elementWidthWithLetterSpacing = letterSpace.getBoundingClientRect().width;
        test(function() {
            assert_equals(elementWidthWithVariantLigatureAndLetterSpacing, elementWidthWithLetterSpacing, "Ligature not applied due to letter spacing.");
            }, "Ligature expected not to be applied due to letter spacing.");
    }

    function testLetterSpaceDoesNotPreventOnum() {
      var elementHeightWithLetterSpacing = onumSpanLetterSpace.getBoundingClientRect().height;
      var elementHeightRegular = onumSpanRegular.getBoundingClientRect().height;
      test(function() {
        assert_equals(elementHeightWithLetterSpacing, elementHeightRegular, "Letter spacing does not prevent enabling 'onum' font feature.");
        }, "Non-ligature font feature expected to be applied despite letter spacing.");
    }

    function runTest() {
        testLetterSpaceAndFeatureLigature();
        testLetterSpaceAndVariantLigature();
        testLetterSpaceDoesNotPreventOnum();
        done();
    }
</script>
</head>
<body onload="runTest();">
<div class="dligDiv common">
    <span id="dligSpan">CACACACA</span>
</div>

<div class="fontVariantDiv common">
    <span id="fontVariantSpan">CACACACA</span>
</div>

<div class="common">
    <span id="letterSpace">CACACACA</span>
</div>

<div class="onumDiv common">
    <span id="onumSpanLetterSpace">56</span>
</div>

<div class="onumDiv common noLetterSpace">
    <span id="onumSpanRegular">56</span>
</div>

</body>
</html>
