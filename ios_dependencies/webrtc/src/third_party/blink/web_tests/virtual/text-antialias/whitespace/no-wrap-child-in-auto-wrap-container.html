<!DOCTYPE html>
<html>
    <head>
        <style type="text/css">
            #container {
                width: 200px;
            }
            #float {
                float: left;
                height:50px;
                width: 50px;
                background-color:red;
            }
            #nowrap {
                white-space: nowrap;
            }
         </style>
    </head>
    <body>
        A no-wrap child that does not fit on an autowrap line should shift down even when there
        is no whitespace after the no-wrap child.
        <!-- The lack of whitespace between </span> and </div> in this case below was preventing WebKit from moving the line down beneath the float -->
        <div id="container">
            <div id="float"></div>
            <span id="nowrap">This text should be under the red square.</span></div>
        <br>
        <div id="container nowrap">
            <div id="float"></div>
            <span id="nowrap">This text should be to the right of the red square.</span></div>
    </body>
</html>
 
