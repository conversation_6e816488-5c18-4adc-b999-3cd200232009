<!DOCTPYE html>
<meta charset="utf-8">
<script src="../../resources/testharness.js"></script>
<script src="../../resources/testharnessreport.js"></script>
<style>
html, body { margin: 0; }
</style>
<div id="log"></div>
<script>
var range = document.createRange();
generate_tests(testGrapheme, [
  ["Latin Combining Diacritical Marks U+301E at start", "a\u0301e", 0, 2],
  ["Latin Combining Diacritical Marks U+301E at mid", "ea\u0301e", 1, 3],
  ["Latin Combining Diacritical Marks U+301E at end", "ea\u0301", 1, 3],
  ["Arabic Fatha U+064E at start", "\u0648\u064E\u064A", 0, 2],
  ["Arabic Fatha U+064E at mid", "\u064A\u0648\u064E\u064A", 1, 3],
  ["Arabic Fatha U+064E at end", "\u064A\u0648\u064E", 1, 3],
]);

function testGrapheme(text, startOffset, endOffset) {
  var textNode = document.createTextNode(text);
  var div = document.createElement("div");
  div.appendChild(textNode);
  document.body.appendChild(div);

  var rects = getClientRects(textNode, startOffset, endOffset);
  assert_equals(rects.length, 1);
  var expected = rects[0];

  for (var i = startOffset; i < endOffset; i++) {
    rects = getClientRects(textNode, i, i + 1);
    assert_equals(rects.length, 1);
    assert_equals_rect(rects[0], expected, "[" + i + "]");
  }
}

function getClientRects(node, start, end) {
  range.setStart(node, start);
  range.setEnd(node, end);
  return range.getClientRects();
}

function assert_equals_rect(actual, expected, description) {
  for (var prop in expected)
    assert_equals(actual[prop], expected[prop], description + "." + prop);
}
</script>
