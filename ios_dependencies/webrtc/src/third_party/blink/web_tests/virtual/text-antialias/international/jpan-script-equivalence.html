<!DOCTYPE html>
<html>
<head>
<script>
if (window.internals) {
    internals.settings.setStandardFontFamily("Ahem", "Hrkt");
    internals.settings.setStandardFontFamily("Ahem", "Hang");
}
</script>
</head>
<body>
<!-- bug 88845.  Test that the various Japanese script codes ("Hira", "Hrkt", "Kana",
"Jpan") are treated the same for assigning a per-script font setting. Similarly
with Korean script codes. The test passes if all divs are in Ahem font.
-->
<div style="font-size: 20px">
<div lang="ja-Hira">this is ahem font</div>
<div lang="ja-Hrkt">this is ahem font</div>
<div lang="ja-Jpan">this is ahem font</div>
<div lang="ja-Kana">this is ahem font</div>
<div lang="ko-Hang">this is ahem font</div>
<div lang="ko-Kore">this is ahem font</div>
</div>
</body>
</html>
