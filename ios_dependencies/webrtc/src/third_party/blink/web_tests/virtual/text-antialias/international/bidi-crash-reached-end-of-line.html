<body onload="runTest();">
        <script>
        if (window.testRunner)
           testRunner.dumpAsText();

        function reference(domNode)
        {
            this.domNode = domNode;
        }
        function walk(a, currentPrefix, index, domNode)
        {
            if (domNode == null)
                return;
            newPrefix = currentPrefix + "_" + index;
            walk(a, currentPrefix, index + 1, domNode.nextSibling);
            walk(a, newPrefix, 0, domNode.firstChild);
            a[newPrefix] = new reference(domNode);
        }
        function clearAllNodes()
        {
            var a = new Array();
            walk(a, "", 0, document.body);
            for (key in a)
            {
                document.body.offsetTop;
                child = a[key].domNode.parentNode.removeChild(a[key].domNode);
            }
        }
        function runTest() {
          clearAllNodes();
          document.body = document.createElement("body");
          document.body.innerHTML = "This test has passed if it doesn't crash under ASAN";
        }
        </script>>>>>>><legend style="widows:994907373; padding-right:493197256%; ">><h1 accesskey="5N'" class="Y990" dir="rtl" style="direction:rtl; line-height:20034836;" id="8" lang="tr" >>>>><abbr title="@u\>qr$^" accesskey="" tabindex="2147483648" title="\" title="QA'>9JQT" style="border-top-width:medium; white-space:pre-line; " id="b71pkZOs" >>><center>,</center>
<dl>></dl>
<output>nnlN8rc<b></b>
</output>
> lang="xh" ></i>
<output></output>
</abbr>
<sup>>
