<!DOCTYPE html>
<html>
    <head>
        <meta charset="utf-8">
        <style>
            #test {
                background: silver;
                padding: 15px;
                width: 30ex;
            }
            #test > div { 
                font-size: 3em;
                background: white;
                display: inline-block;
            }
            .highlight {
                position: absolute;
                border: 10px solid silver;
            }

        </style>
    </head>
    <body>
        <section id="test">
            <div>ااااا<bdo>کبند</bdo></div><br><br>
            <div><bdo>کبند</bdo></div><br><br>
            <div><bdo>کبند کبند</bdo></div><br><br>
            <div>ااااا<bdo dir="ltr">کبند</bdo></div><br><br>
            <div>ااااا<bdo dir="rtl">کبند</bdo></div><br><br>
            <div>ااااا<bdo dir="auto">کبند</bdo></div>
        </section>
        <section>
            <p>
                Tests handling of <code>bdo</code> tags. The white box
                for each line above should fully contain the text and
                characters should not be painted on top of other
                characters.
            </p>
        </section>
        <script>
            function createHighlightRect(rect)
            {
                var el = document.createElement('div');
                el.className = 'highlight';
                el.style.left = (rect.left - 10) + 'px';
                el.style.top = (rect.top - 10) + 'px';
                el.style.width = (rect.width + 5) + 'px';
                el.style.height = (rect.height + 5) + 'px';
                return el;
            }
            var testElements = document.getElementById('test').getElementsByTagName('div');
            for (var el, i = 0; el = testElements[i]; i++) {
                var rect = el.getBoundingClientRect();
                document.body.appendChild(createHighlightRect(rect));
            }
        </script>
    </body>
</body>
