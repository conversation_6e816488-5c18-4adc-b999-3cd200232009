<!DOCTYPE html>
<style>
  #t1::before {
    content: "A";
    display: block
  }
  #t1.inline::before {
    display: inline
  }

  #t2::before {
    content: "C";
    display: block
  }
  #t2.inline::before {
    display: inline
  }
</style>
<p>There should be a space between A and B.</p>
<div id="t1"> <span>B</span></div>
<p>There should be a space between C and D.</p>
<div id="t2"></div>
<script>
  t2.attachShadow({mode:"open"}).innerHTML = " <span>D</span>";
  document.body.offsetTop; // Force layout tree generation.

  t1.className = "inline";
  t2.className = "inline";
</script>
