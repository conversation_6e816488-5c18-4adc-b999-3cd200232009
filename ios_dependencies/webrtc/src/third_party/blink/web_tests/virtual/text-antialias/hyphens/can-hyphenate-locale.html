<!DOCTYPE html>
<script src="../../../resources/testharness.js"></script>
<script src="../../../resources/testharnessreport.js"></script>
<script>
'use strict';
(function () {
  test(function () {
    assert_own_property(window, 'internals');
  }, 'This test requires "internals.canHyphenate"');

  ['en-us', 'en-gu'].forEach(function (locale) {
    test(function () {
      assert_true(internals.canHyphenate(locale));
    }, `"${locale}" can hyphenate`);
  });

  ['ja-jp'].forEach(function (locale) {
    test(function () {
      assert_false(internals.canHyphenate(locale));
    }, `"${locale}" cannot hyphenate`);
  });
})();
</script>
