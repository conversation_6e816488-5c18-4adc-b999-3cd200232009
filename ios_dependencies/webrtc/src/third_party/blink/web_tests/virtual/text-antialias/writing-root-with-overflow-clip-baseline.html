<!DOCTYPE html>
<style>
body {
    font: 10px/1 Ahem;
}

#inlineBlock {
    display: inline-block;
}

#verticalTextWithOverflowClip {
    display: inline-block;
    -webkit-writing-mode: vertical-rl;
    writing-mode: vertical-rl;
    overflow: hidden;
    margin-bottom: 100px;
    /* Hide the vertical text in the output for ref-test. */
    visibility: hidden;
}
</style>
<div>This test checks baselines on writing mode boundaries with an overflow clip.</div>
<div id="inlineBlock">There should be about 100px above this line.<span id="verticalTextWithOverflowClip">Text.</span></div>
