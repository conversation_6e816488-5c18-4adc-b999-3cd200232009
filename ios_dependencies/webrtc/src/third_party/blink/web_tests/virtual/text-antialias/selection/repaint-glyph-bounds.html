<!DOCTYPE html>
<html>
    <head>
        <meta charset="utf-8">
        <style>
            #test {
                font-family: arial;
                font-size: 300%;
            }
        </style>
    </head>
    <body>
        <section id="test">
            بِِِِِِِِِِِِِِِِِِِِِِِِِِِِِِِا<br><span>test 1</span><br><span>test 2</span>
        </section>
        <section>
            <p>
                Tests that the glyph overflow rect, used for repaint, is
                correctly computed. After selecting and unselecting either
                <code>test</code> line above the harakat from the first line are
                correctly repainted.
            </p>
        </section>
        <script>
            if (window.testRunner)
                testRunner.waitUntilDone();
            
            var testElements = document.getElementById('test').
                getElementsByTagName('span');

            window.setTimeout(function() {
                var text = testElements[0].firstChild;
                window.getSelection().setBaseAndExtent(text, 0, text, text.length);
                window.setTimeout(function() {
                    var text = testElements[1].firstChild;
                    window.getSelection().setBaseAndExtent(text, 0, text, text.length);
                    window.setTimeout(function() {
                        var text = testElements[1].firstChild;
                        window.getSelection().setBaseAndExtent(text, 3, text, text.length);
                        if (window.testRunner)
                            testRunner.notifyDone();
                    }, 0);
                }, 0);                
            }, 0);
        </script>
    </body>
</html>
