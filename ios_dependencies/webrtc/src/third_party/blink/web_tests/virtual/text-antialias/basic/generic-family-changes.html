<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.0//EN">
<head>
<style>
.serif { font-family: serif }
</style>
</head>
<body>
Tests of WebKit's interpretation of font sizes when no absolute font size is specified.  Percentages and logical keywords scale
to reflect the family type.  Opera 9 matches this behavior as well (except it has a bug with multiple font-family mappings
as in the first example).

<pre><span class="serif" style="font-size:medium; font-family: monospace">Should be 13 px</span></pre>

<pre><span style="font-size:13px;font-family:Times">Should be 13px</span></pre>
<pre><span style="font-size:small;font-family:Times">Should be 13px</span></pre>
<p><span style="font-size:small;font-family:Times">Should be 13px</span></p>

<p><tt style="font-size:16px"><span style="font-family:Times">Should be 16px</font></tt></p>
<p><tt><span style="font-size:100%;font-family:Times">Should be 16px</font></tt></p>
<p><tt><span style="font-size:larger;font-family:Times">Should be 19px</font></tt></p>
