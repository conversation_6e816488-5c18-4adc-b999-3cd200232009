<!DOCTYPE HTML>
<html>
<head>
<title>text-combine parser test</title>
<script>
function print(message)
{
    var paragraph = document.createElement("li");
    paragraph.appendChild(document.createTextNode(message));
    document.getElementById("console").appendChild(paragraph);
}
function test()
{
    if (window.testRunner)
        testRunner.dumpAsText();

    var textCombineValues = ["none", "horizontal"];
    var pElems = document.getElementsByTagName("div");
    for ( i = 0 ; i < pElems.length ; ++i )
    {
        var elem = pElems[i];
        elem.style.webkitTextCombine = textCombineValues[i];
        print("-webkit-text-combine:" + elem.style.webkitTextCombine);
    }
}
</script>
</head>
<body onload="test()">
<p>text-combine parser test</p>
<div>-webkit-text-combine:none</div>
<div>-webkit-text-combine:horizontal</div>
<p>Okay if -webkit-text-combine values above and below are the same.</p>
<hr>
<p><ol id=console></ol></p>
</body>
</html>
