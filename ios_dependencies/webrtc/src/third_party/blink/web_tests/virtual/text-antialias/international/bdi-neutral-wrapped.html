<!DOCTYPE html>
<html>
  <head>
    <meta charset="utf-8">
    <title>HTML Test: BDI: neutral when wrapped</title>
    <link rel="reference" href="bdi-neutral-wrapped-ref.html">
    <link rel="author" title="<PERSON><PERSON><PERSON>" href="mailto:<EMAIL>">
    <link rel="author" title="HTML5 bidi test WG" href="mailto:<EMAIL>">
    <link rel="help" href="http://dev.w3.org/html5/spec/Overview.html#the-bdi-element">
    <meta name="assert" content="
      'For the purposes of applying the bidirectional algorithm to the paragraph-level
      container that a bdi element finds itself within, the bdi element must be treated
      like a U+FFFC OBJECT REPLACEMENT CHARACTER.'

      Obviously, this should hold even if the BDI's content is wrapped over more than one line.
      A single character (U+FFFC or otherwise) obviously never gets wrapped over more than one
      line, but we still expect the part of the content preceding the BDI, if any, that is
      displayed on the same line as some part of the BDI to be ordered the same as it would be
      if that part of the BDI were replaced with U+FFFC. Similarly, we expect the part of the
      content following the BDI, if any, that is displayed on the same line as some part of the
      BDI to be ordered the same as it would be if that part of the BDI were replaced with U+FFFC.
      
      In the test below, the content surrounding the BDI forms a single directional run (despite
      the containing element and the BDI both having the opposite direction, because the BDI is
      treated as a neutral). Thus, on the line containing the first part of the BDI, the BDI's
      content appears after the content preceding it, and on the line containing the last part of
      the BDI, the BDI content appears before the content following it, where both 'before' and
      'after' are defined relative to the surrounding content's direction. And, of course, the line
      containing just BDI content should be ordered in the BDI's direction.">
    <style>
      body{
        font-size:2em;
      }
      .box {
        border: medium solid gray;
        width: 400px;
        margin: 20px;
        line-height: 1.2;
      }
    </style>
  </head>
  <body>
    The two boxes below should look exactly the same.
    <!-- Key to entities used below:
      &#x05D0; ... &#x05D5; - The first six Hebrew letters (strongly RTL).
      &#x202D; - The LRO (left-to-right-override) formatting character.
      &#x202C; - The PDF (pop directional formatting) formatting character; closes LRO. -->
    <div class="box">
      <div dir="ltr">
        &#x05D0; &gt;
        <bdi>b &gt;&gt;&gt;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
          c</bdi>
        &gt; &#x05D3;...
      </div>
      <div dir="rtl">
        a &gt;
        <bdi>&#x05D1; &gt;&gt;&gt;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
          &#x05D2;</bdi>
        &gt; d...
      </div>
    </div>
    <div class="box">
      <div dir="ltr">&#x202D;b &lt; &#x05D0;&#x202C;<br/>&#x202D;&gt;&gt;&gt;&#x202C;<br/>&#x202D;&#x05D3; &lt; c...&#x202C;</div>
      <div dir="rtl">&#x202D;a &gt; &#x05D1;&#x202C;<br/>&#x202D;&lt;&lt;&lt;&#x202C;<br/>&#x202D;...&#x05D2; &gt; d&#x202C;</div>
    </div>
  </body>
</html>
