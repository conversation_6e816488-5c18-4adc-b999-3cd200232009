<body>
    <script>
        function addTest(scaleFactor)
        {
            var div = document.createElement("div");
            div.style.float = "left";
            div.style.marginRight = 100 * (scaleFactor - 1) + "px";
            div.style.webkitTransform = "scale(" + scaleFactor + ")";
            div.style.webkitTransformOrigin = "0 0";
            div.style.textDecoration = "underline";
            div.style.fontFamily = "Ahem";
            for (var i = 0; i < 15; ++i) {
                div.appendChild(document.createTextNode("\u00a0\u00a0\u00a0\u00a0"));
                div.appendChild(document.createElement("br"));
            }
            document.body.appendChild(div);
        }

        addTest(1.15);
        addTest(1.2);
        addTest(1.22);
        addTest(1.3);
        addTest(1.45);
        addTest(1.55);
        addTest(1.75);
    </script>
</body>
