<!DOCTYPE html>
<html>
<style>
@font-face { font-family: A; src: url('custom-font-data-crash2.html'); }
#y { font-family: A; }
#y:first-letter { content: "A"; }
</style>
<body>
<div id="y">EFGH</div>
</body>
<script>
function finish() {
    document.body.innerHTML = "PASS";
    if (window.testRunner) {
        // Force style recalc to trigger console messages before done.
        // Workaround for crbug.com/1362133.
        document.body.offsetLeft;
        testRunner.notifyDone();
    }
}

if (window.testRunner) {
    testRunner.dumpAsText();
    testRunner.waitUntilDone();
}

document.designMode = 'on';
document.execCommand('selectall');

// Let the font load to finish.
setTimeout("finish()", 50);
</script>
<style>
#y:before { content: "ABCD"; }
</style>
</body>
</html>