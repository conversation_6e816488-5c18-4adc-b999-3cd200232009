<p>
    This tests text with characters that have multiple glyphs.
</p>
<div id="target" style="font-size: 48px;"><span>[</span>&#x0E04;&#x0E33;&#x0E1C;<span>]</span></div>
<div id="reference" style="font-size: 48px;"><span>[</span>&#x0E04;&#x0E33;<span>]</span></div>
<p id="result">Test did not run</p>
<script>
    if (window.testRunner)
        testRunner.dumpAsText();

    var target = document.getElementById("target");

    var textNodeTarget = target.firstChild.nextSibling;
    var range = document.createRange();
    range.setStart(textNodeTarget, 0);
    range.setEnd(textNodeTarget, 2);
    var targetWidth = range.getClientRects()[0].width;

    var reference = document.getElementById("reference");
    var textNodeReference = reference.firstChild.nextSibling;
    range.setStart(textNodeReference, 0)
    range.setEnd(textNodeReference, 2);
    
    var referenceWidth = range.getClientRects()[0].width;

    document.getElementById("result").innerText = Math.abs(targetWidth - referenceWidth) <= 1 ? "PASS" : "FAIL: width was " + targetWidth + " instead of " + referenceWidth;
</script>
