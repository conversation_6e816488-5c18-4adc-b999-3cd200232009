<!DOCTYPE html>
<html>
<head>
<title>Line breaks after empty inline - Hebrew</title>
<script src="../../resources/testharness.js"></script>
<script src="../../resources/testharnessreport.js"></script>
</head>
<body>
<meta charset=utf-8>
<div>
The following two paragraphs should have the same line breaks:
</div>
<div style="width: 40px; font-family: raanana;">
<p style="border:solid green 1px;">&#x05d0;&#x05d1;&#x05d2; &#x05d3;&#x05d4;&#x05d5; &#x05d6;&#x05d7;&#x05d8;</p>
<p style="border:solid green 1px;">&#x05d0;&#x05d1;&#x05d2; <span></span> &#x05d3;&#x05d4;&#x05d5; &#x05d6;&#x05d7;&#x05d8;</p>
</div>
<div><pre id=results></pre></div>
<script>
function getLineWidths(paragraphNumber) {
    var range = document.createRange();
    var paragraphs = document.getElementsByTagName("p");
    var p = paragraphs[paragraphNumber];
    range.setStart(p, 0);
    range.setEnd(p,p.childNodes.length);
    var rects = range.getClientRects();
    var widths = [];
    for (var i = 0; i < rects.length; ++i) {
        var r = rects[i];
        if (r.width != 0)
            widths.push(r.width);
    }
    return widths;
}

test(() => {
  var widths1 = getLineWidths(0);
  var widths2 = getLineWidths(1);
  assert_equals(widths1.length, widths2.length,
    'FAIL: different number of lines, got ' + widths2.length + ', expected ' + widths1.length);

  for (var i = 0; i < widths1.length; ++i) {
    assert_equals(widths2[i], widths1[i],
      'FAIL: line[' + i + '] width differs, got ' + widths2[i] + ', expected ' + widths1[i]);
  }
});
</script>
</body>
</html>
