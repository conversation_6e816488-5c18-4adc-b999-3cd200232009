<script src="../../../resources/ahem.js"></script>
<style>
    div > div { width: 100px; font-family: Ahem; font-size: 20px; }
    div > div::selection { color: green; }
</style>
<div style="width: 100px;">
<div id="target">xxxxx xxxxx</div><div>xxxxx xxxxx</div>
</div>
<script>
    var target = document.getElementById("target");
    getSelection().setBaseAndExtent(target.firstChild, 6, target.nextSibling.firstChild, 5);
</script>
