<!DOCTYPE html>
<html>
    <head>
        <title>Tests handling of unmatched surrogate pairs</title>
        <meta charset="utf8">
    </head>
    <body>
        <div>Full codepoint, "🌎". Prints a globe glyph.</div>
        <div>First part of surrogate pair, "�". Should print replacement character and rest of run.</div>
        <div>Second part of surrogate pair, "�". Should print replacement character and rest of run.</div>
        <br>
        <div>Spanning text nodes:</div>
        <!-- Rendering of unmatched surrogate pair is not interoperable.
        <div>- First part "��" second part.</div>
        -->
        <div>After element.normalize():</div>
        <div>- First part "🌎" second part.</div>
    </body>
</html>
