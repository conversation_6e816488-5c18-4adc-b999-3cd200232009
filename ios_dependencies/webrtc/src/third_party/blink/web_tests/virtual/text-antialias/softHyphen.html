<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" 
"http://www.w3.org/TR/html4/loose.dtd">
<html>
<head>
<title>Soft hyphen displayed when first character</title>
</head>
<body>
<p>
In all of the following, there should not be a hyphen before &ldquo;lorem&rdquo;.
</p>

<p>Do<span style="float:right; display:inline-block; border:1px solid red;">X</span>&shy;lorem ipsum</p>

<p><span style="float:right; display:inline-block; border:1px solid red;">X</span>&shy;lorem ipsum</p>


<p>Do<span style="position:relative; display:inline-block; border:1px solid red;">X</span>&shy;lorem ipsum</p>

<p>Do<span style="position:absolute; right:0; display:inline-block; border:1px solid red;">X</span>&shy;lorem ipsum</p>

Do<p>&shy;lorem ipsum</p>

<p>Do<br/>&shy;lorem ipsum</p>

<p>Do<span>&shy;lorem</span> ipsum</p>

<p>D<span>o</span>&shy;lorem ipsum</p>

<p>Do <span>&shy;lorem</span> ipsum</p>

<p>D<span>o </span>&shy;lorem ipsum</p>

<p>Do  <span>&shy;lorem</span> ipsum</p>

<p>D<span>o  </span>&shy;lorem ipsum</p>

<p>Do &shy; lorem</p><!-- WinIE displays as two spaces -->
<p>Do&shy; &shy;lorem</p>
<p>Do  &shy;lorem</p>
<p>Do&shy;  lorem</p>
<p>Do &shy;  lorem</p><!-- WinIE displays as two spaces -->
<p>Do  &shy; lorem</p>

<p>Do<span></span>&shy;lorem ipsum</p>

<p>Do&shy;&shy;lorem ipsum</p>

<p><span>Do&shy;</span>&shy;lorem ipsum</p>
<p>Do&shy;<span>&shy;lorem ipsum</span></p>
<p><span>Do&shy;&shy;</span>lorem ipsum</p>

<p>The following pair should be the same:</p><!-- LayoutText fix -->

<p>W<span style="display:inline-block; border:1px solid red;">&shy;X</span>Y</p>
<p>W<span style="display:inline-block; border:1px solid red;">X</span>Y</p>

</body>