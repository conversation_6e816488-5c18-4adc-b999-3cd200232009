<html>
<head>
<style>
body {
    -webkit-writing-mode: vertical-lr;
}
#test {
    -webkit-text-combine: horizontal;
    height: 7px;
    white-space: pre-wrap;
}
</style>
<script>
function runTest() {
    if (window.testRunner)
        testRunner.dumpAsText();
    document.body.offsetTop;
    var testContainer = document.getElementById('test');
    testContainer.parentNode.removeChild(testContainer);
}
</script>
</head>
<body onload='runTest()'><p>Test passes if there's no crash.</p>
<!-- The empty scripts tags are needed to split the LayoutTextCombine and reproduce the bug. -->
<div id="test"><p>foo   <script></script>
  <script></script>
</p>
</div>
</body>