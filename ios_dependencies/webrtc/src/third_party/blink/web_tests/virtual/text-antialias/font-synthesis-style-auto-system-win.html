<!DOCTYPE html>
<html lang="en">
<meta charset="utf-8"/>
<title>CSS Test: font-synthesis-style: auto enables fake italic/oblique</title>
<link rel="help" href="https://www.w3.org/TR/css-fonts-4/#font-synthesis-style">
<meta name="assert" content=" If ‘style’ is specified, user agents must synthesize italic faces">
<!--Sylfaen is only available in Regular style in the Windows set of test fonts that are used as system fonts in Windows testing-->
<style>
    @supports not (font-synthesis-style: auto) {
        .test {color: red;}
    }
    .test {
        font-family: Sylfaen;
        font-size: 3em;
        font-style: italic;
    }
    .nosynth {
        font-synthesis-style: auto;
    }
</style>

<p>Test passes if the two lines below are identical and there is no red.</p>
<section class="test">
    <p>текст-заполнитель</p>
    <p class="nosynth">текст-заполнитель</p>
</section>
