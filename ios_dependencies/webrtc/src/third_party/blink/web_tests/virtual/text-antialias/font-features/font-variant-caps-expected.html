<!DOCTYPE HTML>
<!-- Test case adapted from Mozilla tests for font-variant-subproperties available under
     http://creativecommons.org/publicdomain/zero/1.0/
     See discussion on https://bugzilla.mozilla.org/show_bug.cgi?id=1261445
-->
<html lang="en">
<head>
<title>font-variant-caps test</title>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
<script type="text/javascript" src="resources/gsubtest-features.js"></script>
<script type="text/javascript" src="resources/font-variant-features.js"></script>
<link rel="stylesheet" href="resources/font-variant-features.css" type="text/css"/>
</head>
<body>
<div id="content"></div>
<script type="text/javascript">
  document.getElementById("content").appendChild(createFeatureTestTable(gPropertyData, "font-variant-caps", true, false));
</script>
</body>
</html>
