<!DOCTYPE html>

<html>
<head>
  <title>Testing Khmer selection</title>
  <meta http-equiv="content-type" content="text/html; charset=UTF-8">
  <script>
    function main() {
      const target = document.getElementById('target');
      let y = 5;
      for (let element = target; element; element = element.offsetParent)
        y += element.offsetTop;
      eventSender.mouseMoveTo(5, y);
      eventSender.mouseDown();

      for (var x = 5; x < 600; x += 5) {
        eventSender.mouseMoveTo(x, y);
      }

      eventSender.mouseUp();
    }
  </script>
</head>

<body onload="main();">
  <p id="target">
    <nobr><b class="gb1">វ៉ែប</b> <a href=
    "http://images.google.com.kh/imghp?hl=km&amp;tab=wi" class=
    "gb1">រូបភាព</a> <a href=
    "http://groups.google.com.kh/grphp?hl=km&amp;tab=wg" class=
    "gb1">ក្រុម</a> <a href=
    "http://www.google.com.kh/dirhp?hl=km&amp;tab=wd" class=
    "gb1">ថតឯកសារ</a></nobr>
  </p>
  <p>Some Harfbuzz shapers will output cluster logs which suggest that some glyphs had <i>no</i> contributing code points. This test contains such text and uses eventSender to select the text. You should see some of the above text selected and test_shell shouldn't trigger any assertions or crash.</p>
</body>
</html>
