<!DOCTYPE html> 
<html>
<body> 
<p>This test ensures WebKit renders text node between two BR elements in a pre when inserted by a script immediately after triggering a layout. You should see PASS below:</p>
<pre id="test" style="width: 10ex;">---------- -----<br><br></pre> 
<script> 

document.body.offsetTop;

var text = document.createTextNode('text');
var span = document.createElement('span');
span.appendChild(text);
test.insertBefore(span, document.getElementsByTagName('br')[1]);

var passed = span.offsetHeight > 0 ? true : false;
test.parentNode.removeChild(test);

if (window.testRunner)
    testRunner.dumpAsText();
document.writeln(passed ? 'PASS' : 'FAIL');

</script>
</body>
</html>
