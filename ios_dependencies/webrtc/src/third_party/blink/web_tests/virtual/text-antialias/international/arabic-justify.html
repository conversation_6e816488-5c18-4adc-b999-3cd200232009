﻿<!DOCTYPE html>
<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
</head>
<body>
<p>The following text should be rendered as 3 lines with justification. The spaces are evenly distributed over (the first line of) text.</p>
<div style='width: 100px; direction: rtl; text-align: justify;'>&#x648;&#x644;&#x64A;&#x20;&#x627;&#x644;&#x639;&#x647;&#x62F;&#x20;&#x64A;&#x648;&#x62C;&#x647;&#x20;&#x628;&#x62A;&#x643;&#x631;&#x64A;&#x645;&#x20;&#x645;&#x648;&#x627;&#x637;&#x646;&#x20;&#x648;&#x645;&#x642;&#x64A;&#x645;&#x20;&#x623;&#x646;&#x642;&#x630;&#x627;</div>

<br />
<p> The following text should be rendered as 2 lines with justification. The 1st line should be totally justified ( which means 
aligned with the 2nd line at right side) and there should be no text overlapping in the 1st line.
</body>
<div style='width: 250px; direction: rtl; text-align: justify;'>الخميس.
وقال المتحدث احمد فوزي "ما نتوقعه في العاشر  
</div>
</html>
