<!DOCTYPE html>
<style>
.float-left {
    float: left;
}
</style>
<p id="p"></p>
<script>
window.onload = function (e) {
    document.body.offsetTop;
    var p = document.getElementById('p');
    var firstSpan = document.createElement("span");
    firstSpan.setAttribute("class", "float-left");
    firstSpan.appendChild(document.createTextNode("e"));
    var secondSpan = document.createElement("span");
    secondSpan.setAttribute("class", "float-left");
    secondSpan.appendChild(document.createTextNode("x"));

    p.appendChild(document.createTextNode("t"));
    p.appendChild(firstSpan);
    p.appendChild(document.createTextNode(" "));
    p.appendChild(secondSpan);
    p.appendChild(document.createTextNode("t"));


};
</script>
<p>crbug.com/556733: Ensure whitespace gets created when we attached siblings from back to front. There should be a space between the t and the t.</p>

