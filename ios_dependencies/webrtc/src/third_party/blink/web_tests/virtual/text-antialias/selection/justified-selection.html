<style>
    ::selection { color: green; background-color: yellow; }
</style>
<p>
    Test for revision <a href="http://trac.webkit.org/projects/webkit/changeset/20574">#20574</a>.
</p>
<p>
    The two blue boxes should be identical.
</p>
<div style="text-align: justify; width: 100px; border: solid blue;">L<span style="background-color: yellow; color: green;">o r</span>e mi psumdolor</div>
<br>
<div id="target" style="text-align: justify; width: 100px; border: solid blue;">Lo re mi psumdolor</div>
<script>
    var text = document.getElementById("target").firstChild;
    getSelection().setBaseAndExtent(text, 1, text, 4);
</script>
