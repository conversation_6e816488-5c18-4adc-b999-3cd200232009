<!DOCTYPE html>
<meta charset="utf-8">
<script src="../../../resources/testharness.js"></script>
<script src="../../../resources/testharnessreport.js"></script>
<style>
/* Needs font specifications that do not trigger fallback differences
   between base and synthetic small-caps (i.e. upper cased) versions. */
span {
font-family: DejaVu Sans, Arial Unicode MS, Arial;
}

.caps { font-variant-caps: all-small-caps; font-size: 50px; }

.synthetic {
font-size: 35px; // 0.7 * caps font size, see SimpleFontData.cpp
}
</style>

<!-- Concatenated list of all non context-sensitive regular-uppercase pairings from ftp://ftp.unicode.org/Public/UCD/latest/ucd/SpecialCasing.txt -->
<span id="allsmallcaps" class="caps">&#x00DF;&#x0130;&#xFB00;&#xFB01;&#xFB02;&#xFB03;&#xFB04;&#xFB05;&#xFB06;&#x0587;&#xFB13;&#xFB14;&#xFB15;&#xFB16;&#xFB17;&#x0149;&#x0390;&#x03B0;&#x01F0;&#x1E96;&#x1E97;&#x1E98;&#x1E99;&#x1E9A;&#x1F50;&#x1F52;&#x1F54;&#x1F56;&#x1FB6;&#x1FC6;&#x1FD2;&#x1FD3;&#x1FD6;&#x1FD7;&#x1FE2;&#x1FE3;&#x1FE4;&#x1FE6;&#x1FE7;&#x1FF6;&#x1F80;&#x1F81;&#x1F82;&#x1F83;&#x1F84;&#x1F85;&#x1F86;&#x1F87;&#x1F88;&#x1F89;&#x1F8A;&#x1F8B;&#x1F8C;&#x1F8D;&#x1F8E;&#x1F8F;&#x1F90;&#x1F91;&#x1F92;&#x1F93;&#x1F94;&#x1F95;&#x1F96;&#x1F97;&#x1F98;&#x1F99;&#x1F9A;&#x1F9B;&#x1F9C;&#x1F9D;&#x1F9E;&#x1F9F;&#x1FA0;&#x1FA1;&#x1FA2;&#x1FA3;&#x1FA4;&#x1FA5;&#x1FA6;&#x1FA7;&#x1FA8;&#x1FA9;&#x1FAA;&#x1FAB;&#x1FAC;&#x1FAD;&#x1FAE;&#x1FAF;&#x1FB3;&#x1FBC;&#x1FC3;&#x1FCC;&#x1FF3;&#x1FFC;&#x1FB2;&#x1FB4;&#x1FC2;&#x1FC4;&#x1FF2;&#x1FF4;&#x1FB7;&#x1FC7;&#x1FF7;</span>
<span id="uppercase" class="synthetic">&#x0053;&#x0053;&#x0130;&#x0046;&#x0046;&#x0046;&#x0049;&#x0046;&#x004C;&#x0046;&#x0046;&#x0049;&#x0046;&#x0046;&#x004C;&#x0053;&#x0054;&#x0053;&#x0054;&#x0535;&#x0552;&#x0544;&#x0546;&#x0544;&#x0535;&#x0544;&#x053B;&#x054E;&#x0546;&#x0544;&#x053D;&#x02BC;&#x004E;&#x0399;&#x0308;&#x0301;&#x03A5;&#x0308;&#x0301;&#x004A;&#x030C;&#x0048;&#x0331;&#x0054;&#x0308;&#x0057;&#x030A;&#x0059;&#x030A;&#x0041;&#x02BE;&#x03A5;&#x0313;&#x03A5;&#x0313;&#x0300;&#x03A5;&#x0313;&#x0301;&#x03A5;&#x0313;&#x0342;&#x0391;&#x0342;&#x0397;&#x0342;&#x0399;&#x0308;&#x0300;&#x0399;&#x0308;&#x0301;&#x0399;&#x0342;&#x0399;&#x0308;&#x0342;&#x03A5;&#x0308;&#x0300;&#x03A5;&#x0308;&#x0301;&#x03A1;&#x0313;&#x03A5;&#x0342;&#x03A5;&#x0308;&#x0342;&#x03A9;&#x0342;&#x1F08;&#x0399;&#x1F09;&#x0399;&#x1F0A;&#x0399;&#x1F0B;&#x0399;&#x1F0C;&#x0399;&#x1F0D;&#x0399;&#x1F0E;&#x0399;&#x1F0F;&#x0399;&#x1F08;&#x0399;&#x1F09;&#x0399;&#x1F0A;&#x0399;&#x1F0B;&#x0399;&#x1F0C;&#x0399;&#x1F0D;&#x0399;&#x1F0E;&#x0399;&#x1F0F;&#x0399;&#x1F28;&#x0399;&#x1F29;&#x0399;&#x1F2A;&#x0399;&#x1F2B;&#x0399;&#x1F2C;&#x0399;&#x1F2D;&#x0399;&#x1F2E;&#x0399;&#x1F2F;&#x0399;&#x1F28;&#x0399;&#x1F29;&#x0399;&#x1F2A;&#x0399;&#x1F2B;&#x0399;&#x1F2C;&#x0399;&#x1F2D;&#x0399;&#x1F2E;&#x0399;&#x1F2F;&#x0399;&#x1F68;&#x0399;&#x1F69;&#x0399;&#x1F6A;&#x0399;&#x1F6B;&#x0399;&#x1F6C;&#x0399;&#x1F6D;&#x0399;&#x1F6E;&#x0399;&#x1F6F;&#x0399;&#x1F68;&#x0399;&#x1F69;&#x0399;&#x1F6A;&#x0399;&#x1F6B;&#x0399;&#x1F6C;&#x0399;&#x1F6D;&#x0399;&#x1F6E;&#x0399;&#x1F6F;&#x0399;&#x0391;&#x0399;&#x0391;&#x0399;&#x0397;&#x0399;&#x0397;&#x0399;&#x03A9;&#x0399;&#x03A9;&#x0399;&#x1FBA;&#x0399;&#x0386;&#x0399;&#x1FCA;&#x0399;&#x0389;&#x0399;&#x1FFA;&#x0399;&#x038F;&#x0399;&#x0391;&#x0342;&#x0399;&#x0397;&#x0342;&#x0399;&#x03A9;&#x0342;&#x0399</span>

<span id="sharpstruncation" class="caps">ßa</span>
<span id="sharpsuppercase" class="synthetic">SSA</span>

<script>
test(function(){
    assert_equals(allsmallcaps.getBoundingClientRect().width,
                  uppercase.getBoundingClientRect().width);
}, "Synthetic small caps should produce the same width as manually uppercased, downscaled text.");


test(function(){
    assert_equals(sharpstruncation.getBoundingClientRect().width,
                  sharpsuppercase.getBoundingClientRect().width);
}, "Strings containing uppercased sharp S should not be truncated.");
</script>
