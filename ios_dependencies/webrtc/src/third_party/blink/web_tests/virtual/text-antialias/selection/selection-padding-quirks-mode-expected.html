<!DOCTYPE html>
<html lang="en"><head>
<title>Selection in quirks mode</title>
<style>
body {
    margin: 0;
    padding: 0;
}

pre {
    margin: 0;
}

code {
    font-size: 14px;
    padding-top: 4px;
    margin-top: 8px;
    line-height: 1.4em;
}
</style>
</head>
<body>
    <pre><code>
      Selection should render the same in both
      standards and quirks mode.
    </code></pre>
    <script>
        var node = document.getElementsByTagName('code')[0].firstChild;
        var range = document.createRange();
        range.setStart(node, node.length - 17);
        range.setEnd(node, node.length - 11);
        window.getSelection().addRange(range);
    </script>
</body>
</html>
