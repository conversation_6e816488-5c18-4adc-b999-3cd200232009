<!DOCTYPE html>
<html>
    <head>
        <title>Selection painting test</title>
        <style>
            div {
                margin: 0;
                padding: 0;
                display: inline-block;
            }
        </style>
    </head>
    <body>
        <div>
            <span>There</span> <span>should</span> <span>be</span>
            <span>no</span> <span>white</span> <span>gaps</span>
            <span>between</span> <span>words</span>.
        </div>
        <script>
            const div = document.querySelector('div');
            // Select from before |<span>There</span>| to after |<span>words</span>.|.
            window.getSelection().setBaseAndExtent(div.firstChild, div.firstChild.length, div.lastChild, 1);
        </script>
    </body>
</html>
