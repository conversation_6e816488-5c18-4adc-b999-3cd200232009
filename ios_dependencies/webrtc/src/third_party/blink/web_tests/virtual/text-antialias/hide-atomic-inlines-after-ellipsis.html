<!DOCTYPE html>
<style>
.container {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  width: 100px;
  padding-right: 400px;
  background: lightgrey;
}

.rtl {
  direction: rtl;
  padding-left: 400px;
  padding-right: 0px;
}

.float {
  float: right;
  width: 10px;
  height: 10px;
  background-color: blue;
}
.inline-block {
  display: inline-block;
  border: 1px solid black;
}
span {
  border: 1px solid black;
}
</style>
<p>crbug.com/534798: Atomic inline elements should be hidden after an ellipsis.</p>
<div class="container">
  <span class="float"></span><span>01234567890123456789</span><span class="inline-block"><span>text</span>inline-block<br>more inline-block</span><span class="inline-block"></span><img src="f" alt="img">
</div>
<div class="container rtl">
  <span class="float" style="float:left;"></span><span>01234567890123456789</span><span class="inline-block"><span>text</span>inline-block<br>more inline-block</span><span class="inline-block"></span><img src="f" alt="img">
</div>

