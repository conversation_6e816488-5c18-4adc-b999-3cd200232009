<head>
<script>

function testChar(a, b, ch)
{
    // Strings a and b selected here do not have any 'interaction' between them.
    var span = document.getElementById("characters");
    span.firstChild.data = a + b;
    var abWidth = span.offsetWidth;
    span.firstChild.data = a;
    var aWidth = span.offsetWidth;
    span.firstChild.data = a + String.fromCharCode(ch) + b;
    var abWithChWidth = span.offsetWidth;

    if (abWithChWidth > abWidth)
        return 1;
    if (abWidth > aWidth)
        return 0;
    return 1;
}

function testWithZeroWidthSpace(a, b) {
    var failedCount = 0;

    failedCount += testChar(a, b, 0xAD);

    // ZWJ (U+200C) and ZWNJ (U+200D) are excluded because they
    // can affect the rendering in complex script text.
    failedCount += testChar(a, b, 0x200B);
    failedCount += testChar(a, b, 0x200E);
    failedCount += testChar(a, b, 0x200F);
    failedCount += testChar(a, b, 0x202A);
    failedCount += testChar(a, b, 0x202B);
    failedCount += testChar(a, b, 0x202C);
    failedCount += testChar(a, b, 0x202D);
    failedCount += testChar(a, b, 0x202E);
    failedCount += testChar(a, b, 0xFEFF);

    return failedCount;
}

function test()
{
    if (window.testRunner)
        testRunner.dumpAsText();

    var failedDevanagariCount = testWithZeroWidthSpace("\u0915\u093E", "\u0916");

    var failedArabicCount = testWithZeroWidthSpace("\u0627\u0644\u0645\u062A\u0648\u0633\u0637\u0020\u200B\u200B\u0647\u0648\u0020\u0020", "\u0647\u0648\u0020");

    var testArea = document.getElementById("testArea");
    testArea.parentNode.removeChild(testArea);

    var result = "";
    if (failedDevanagariCount == 0 && failedArabicCount == 0)
        result = "PASS: All characters had zero-width.";
    else {
        if (failedDevanagariCount > 0)
            result = "FAIL: " + failedDevanagariCount + " characters had non-zero width" +
                 " or failed to get measured when test with Devanagari";
        if (failedArabicCount > 0)
            result += "\nFAIL: " + failedArabicCount + " characters had non-zero width" +
                 " or failed to get measured when test with Arabic";
    }
    document.getElementById("result").firstChild.data = result;
}
</script>
</head>
<body onload="test()">
<p>This test checks various characters that should always be zero width to ensure that they are when enclosed by complex script characters.
The WebKit text system ensures this in a way that's independent of the fonts installed on the system.</p>
<p id="result">FAIL: Script did not run to completion.</p>
<p id="testArea"><span id="characters">&#x0915;&#x093E;&#x0916;</span></p>
</body>
