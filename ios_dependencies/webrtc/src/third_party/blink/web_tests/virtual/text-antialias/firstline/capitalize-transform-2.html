<!DOCTYPE html>
<meta charset="utf-8">
:<script src="../../../resources/testharness.js"></script>
<script src="../../../resources/testharnessreport.js"></script>
<style>
div::first-line {
  text-transform: uppercase;
}
</style>
<!--
<PERSON><PERSON><PERSON> reorder creates two text runs for this text.
The 2nd run starts at offset 4.
Then because the capitalized form of "&#x03B1;&#x0301;"" is one character,
the whole string is shrunk to 3 characters, which is smaller than the
start offset of the 2nd run.
-->
<div lang=el>&#x03B1;&#x0301;&#x03B1;&#x0301;ע</div>
<script>
test(function() {
}, "Should not crash");
</script>
