<html>
<head>
    <script>
        function getText(id) {
            var e = document.getElementById(id);
            
            return e.contentDocument.getElementsByTagName('pre')[0].firstChild.nodeValue;
        }
        
        function runTests() {
            if (window.testRunner)
                testRunner.dumpAsText();

            var cr = getText('cr');
            var lf = getText('lf');
            var crlf = getText('crlf');
            
            var text = "line 1\nline 2\nline 3\n";

            var result = document.getElementById('result');
            
            if (text != cr) {
                result.innerHTML = "FAILED: cr document doesn't match text!"                
                return;
            }

            if (text != lf) {
                result.innerHTML = "FAILED: lf document doesn't match text!"                
                return;
            }

            if (text != crlf) {
                result.innerHTML = "FAILED: crlf document doesn't match text!"                
                return;
            }
            
            result.innerHTML = "SUCCESS"
        }
    </script>
</head>
<body onload="runTests()">
    <iframe id="cr" src="resources/line-breaks-cr.txt"></iframe>
    <iframe id="lf" src="resources/line-breaks-lf.txt"></iframe>
    <iframe id = "crlf" src="resources/line-breaks-crlf.txt"></iframe>
    <div id="result"></div>
</body>
</html>
