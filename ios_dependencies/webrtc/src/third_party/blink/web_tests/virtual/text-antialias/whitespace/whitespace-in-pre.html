<!DOCTYPE html>
<style>
@font-face {
    font-family: Ahem;
    src: url('../../../resources/Ahem.woff2') format('woff2');
}
body {
    font: 10px Ahem;
}
.title {
    font: 12px Times;
}
.break-word {
    word-wrap: break-word;
}
</style>
<p class="title"> The single tab should be on the second line followed by the C. The tab and the C form a single word so do not break.</p>
<div style="width: 80px;">
    <p class="test">A B<span style="white-space:pre">	</span>C</p>
</div>
<p class="title"> The two tabs should be at the start of the second line. The 'pre' and the C form a single word so do not break. </p>
<div style="width: 160px;">
    <p class="test">A B<span style="white-space:pre">		</span>C</p>
</div>
<p class="title"> The two tabs should fit on the first line.  The line can break at the space before the C.</p>
<div style="width: 160px;">
    <p class="test">A B<span style="white-space:pre">		</span> C</p>
</div>

<p class="title"> The single tab should be on the second line by itself. The div allows breaking within words so the word formed by the tab and the C can be split.</p>
<div style="width: 80px;" class="break-word">
    <p class="test">A B<span style="white-space:pre">	</span>C</p>
</div>
<p class="title"> The two tabs should be on the second line by themselves. The div allows breaking within words but it cannot split the two tabs inside a pre.</p>
<div style="width: 110px;" class="break-word">
    <p class="test">A B<span style="white-space:pre">		</span>C</p>
</div>
<p class="title"> The two tabs should fit on the first line.  The div allows breaking within words but it cannot split the two tabs inside a pre. The line can break at the space before the C.</p>
<div style="width: 160px;" class="break-word">
    <p class="test">A B<span style="white-space:pre">		</span> C</p>
</div>
<script>
    if (window.testRunner) {
        testRunner.waitUntilDone();
        window.onload = function () {
            document.fonts.ready.then(function () { testRunner.notifyDone(); });
        };
    }
</script>
