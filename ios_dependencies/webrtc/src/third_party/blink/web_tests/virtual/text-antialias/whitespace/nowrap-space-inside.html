<!DOCTYPE html>
<script src="../../../resources/testharness.js"></script>
<script src="../../../resources/testharnessreport.js"></script>
<style>
.container {
  width:200px;
  background-color:#eee;
}
.spacer {
  height:10px; background-color:black;
}
.nowrap {
  white-space: nowrap;
}
</style>
<body>
<p>Spaces in nowrap shold not break when the text before it fits but the space overflows.</p>
<template id=template>
  <div class=container>
    <img class=spacer />
    <span class=nowrap><span>foo</span> <span>bar</span></span>
  </div>
</template>
<script>
run();
function run() {
  let tests = [];
  for (let width = 170; width < 180; width++) {
    let element = template.content.children[0].cloneNode(true);
    let spacer = element.querySelector('img');
    spacer.style.width = width + 'px';
    document.body.appendChild(element);
    tests.push({width: width, element: element});
  }
  let results = [];
  for (let t of tests) {
    test(() => {
      let nowrap = t.element.querySelector('.nowrap');
      let child1 = nowrap.children[0];
      let child2 = nowrap.children[1];
      assert_equals(child1.offsetTop, child2.offsetTop);
    }, `width: ${t.width}`);
  }
}
</script>
</body>

