<html>
<head>
<script>
function log(str) {
    var li = document.createElement("li");
    li.appendChild(document.createTextNode(str));
    var console = document.getElementById("console");
    console.appendChild(li);
}

function convertStringToUnicode(string)
{
    var returnValue = " (character in Unicode value): ";
    for (var i = 0; i < string.length; ++i)
    {
        returnValue += " " + string.charCodeAt(i);
    }
    return returnValue;
}

function assertEqual(test_name, actual, expected)
{
    if (actual != expected) {
        log("==================================");
        log("FAILED: " + test_name);
        var actual_string = "actual" + convertStringToUnicode(actual);
        var expected_string = "expected" + convertStringToUnicode(expected);
        log(actual_string);
        log(expected_string);
    }
}

onload = function()
{
    if (window.testRunner)
        testRunner.dumpAsText();
    var div = document.getElementById("div");
    var string = div.innerHTML;
    //should be rendered as "\u0958\u0909 \u00e4" in html.
    assertEqual("devanagari + a with diaeresis", string, "\u0915\u093c\u0909\u0009a\u0308");
}
</script>
</head>
<body>
<div id="div">&#x915;&#x093c;&#x0909;&#x9;a&#x308;</div>
<ul id="console"></ul>
</body>
</html>
