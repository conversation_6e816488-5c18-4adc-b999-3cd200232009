<!doctype html>
<!-- This tests for regression of https://crbug.com/265838 where adjacent, nested isolates caused a use-after-free if the elements were later removed. -->
<script>
window.onload = function()
{
    document.body.offsetTop;
    b.lastChild.parentNode.removeChild(b.lastChild);
    document.body.offsetTop;
    a.firstChild.parentNode.removeChild(a.firstChild);
    document.body.offsetTop;

    document.write("PASS did not crash");
}
</script>

<body>
  <div id="a">foo</div>
  <div></div>
  <div>
    <output>
        <output>bar</output>
        <span id="b">
            <span><div style="display:inline-block"></div><br><br><br></span>
        </span>
    </output>
  </div>
</body>

<script>
if (window.testRunner)
    testRunner.dumpAsText();
</script>
