<!DOCTYPE html>
<html>
<head>
  <title>Ligature Linebreaking</title>
  <script src="../../resources/testharness.js"></script>
  <script src="../../resources/testharnessreport.js"></script>

<style type="text/css">
@font-face {
  font-family: megalopolis;
  src: url(../../third_party/MEgalopolis/MEgalopolisExtra.woff) format("woff");
}

body { 
font-family: megalopolis, sans-serif; 
}

.dlig {
-moz-font-feature-settings:"frac" 1, "dlig" 1; 
-moz-font-feature-settings:"frac=1, dlig=1"; 
-ms-font-feature-settings:"frac" 1, "dlig" 1; 
-o-font-feature-settings:"frac" 1, "dlig" 1; 
-webkit-font-feature-settings:"frac" 1, "dlig" 1; 
font-feature-settings:"frac" 1, "dlig" 1;
font-size: 24px;
line-height: 100%;
padding: 0px;
}

p { font-family: serif; font-style: italic; }

.container {
width: 180px;
background-color: lightpink;
}

</style>
</head>

<body>
<div class="dlig container" >CACACACA CACACACA</div>

<div id="log"></div>
<script>
  setup({ explicit_done: true });
  window.addEventListener("load", function() {
    test(function(){
      assert_true(document.querySelector('.dlig').clientHeight <= 24, "Ligature words laid out in one line.");
    }, "Words expected to be laid out in one line.");
    done();
  });
</script>

</body></html>
