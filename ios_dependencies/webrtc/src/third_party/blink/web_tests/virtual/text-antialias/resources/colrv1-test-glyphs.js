var glyph_descriptions = {
  "util_contours": {
    "codepoints": [
      "\udbb8\udc00",
      "\udbb8\udc01",
      "\udbb8\udc02",
      "\udbb8\udc03",
      "\udbb8\udc04",
      "\udbb8\udc05",
      "\udbb8\udc06",
      "\udbb8\udc07"
    ],
    "axes": []
  },
  "gradient_stops_repeat": {
    "codepoints": [
      "\udb80\udd00",
      "\udb80\udd01",
      "\udb80\udd02",
      "\udb80\udd03"
    ],
    "axes": []
  },
  "sweep_varsweep": {
    "codepoints": [
      "\udb80\ude00",
      "\udb80\ude01",
      "\udb80\ude02",
      "\udb80\ude03",
      "\udb80\ude04",
      "\udb80\ude05",
      "\udb80\ude06",
      "\udb80\ude07",
      "\udb80\ude08",
      "\udb80\ude09",
      "\udb80\ude0a",
      "\udb80\ude0b",
      "\udb80\ude0c",
      "\udb80\ude0d",
      "\udb80\ude0e",
      "\udb80\ude0f",
      "\udb80\ude10",
      "\udb80\ude11",
      "\udb80\ude12",
      "\udb80\ude13",
      "\udb80\ude14",
      "\udb80\ude15",
      "\udb80\ude16",
      "\udb80\ude17",
      "\udb80\ude18",
      "\udb80\ude19",
      "\udb80\ude1a",
      "\udb80\ude1b",
      "\udb80\ude1c",
      "\udb80\ude1d",
      "\udb80\ude1e",
      "\udb80\ude1f",
      "\udb80\ude20",
      "\udb80\ude21",
      "\udb80\ude22",
      "\udb80\ude23",
      "\udb80\ude24",
      "\udb80\ude25",
      "\udb80\ude26",
      "\udb80\ude27",
      "\udb80\ude28",
      "\udb80\ude29",
      "\udb80\ude2a",
      "\udb80\ude2b",
      "\udb80\ude2c",
      "\udb80\ude2d",
      "\udb80\ude2e",
      "\udb80\ude2f",
      "\udb80\ude30",
      "\udb80\ude31",
      "\udb80\ude32",
      "\udb80\ude33",
      "\udb80\ude34",
      "\udb80\ude35",
      "\udb80\ude36",
      "\udb80\ude37",
      "\udb80\ude38",
      "\udb80\ude39",
      "\udb80\ude3a",
      "\udb80\ude3b",
      "\udb80\ude3c",
      "\udb80\ude3d",
      "\udb80\ude3e",
      "\udb80\ude3f",
      "\udb80\ude40",
      "\udb80\ude41",
      "\udb80\ude42",
      "\udb80\ude43",
      "\udb80\ude44",
      "\udb80\ude45",
      "\udb80\ude46",
      "\udb80\ude47"
    ],
    "axes": [
      {
        "tag": "SWPS",
        "name": "Sweep Start Angle Offset",
        "minimum": -90,
        "default": 0,
        "maximum": 90
      },
      {
        "tag": "SWPE",
        "name": "Sweep End Angle Offset",
        "minimum": -90,
        "default": 0,
        "maximum": 90
      },
      {
        "tag": "SWC1",
        "name": "Sweep tests color stop offset 1",
        "minimum": -2,
        "default": 0,
        "maximum": 2
      },
      {
        "tag": "SWC2",
        "name": "Sweep tests color stop offset 2",
        "minimum": -2,
        "default": 0,
        "maximum": 2
      },
      {
        "tag": "SWC3",
        "name": "Sweep tests color stop offset 3",
        "minimum": -2,
        "default": 0,
        "maximum": 2
      },
      {
        "tag": "SWC4",
        "name": "Sweep tests color stop offset 4",
        "minimum": -2,
        "default": 0,
        "maximum": 2
      }
    ]
  },
  "paint_scale": {
    "codepoints": [
      "\udb80\udf00",
      "\udb80\udf01",
      "\udb80\udf02",
      "\udb80\udf03",
      "\udb80\udf04",
      "\udb80\udf05"
    ],
    "axes": [
      {
        "tag": "SCOX",
        "name": "Scale tests, center x offset",
        "minimum": -200,
        "default": 0,
        "maximum": 200
      },
      {
        "tag": "SCOY",
        "name": "Scale tests, center y offset",
        "minimum": -200,
        "default": 0,
        "maximum": 200
      },
      {
        "tag": "SCSX",
        "name": "Scale tests, x or uniform scale",
        "minimum": -2,
        "default": 0,
        "maximum": 1.99993896484375
      },
      {
        "tag": "SCSY",
        "name": "Scale tests, y scale",
        "minimum": -2,
        "default": 0,
        "maximum": 1.99993896484375
      }
    ]
  },
  "extend_mode": {
    "codepoints": [
      "\udb81\udd00",
      "\udb81\udd01",
      "\udb81\udd02",
      "\udb81\udd03",
      "\udb81\udd04",
      "\udb81\udd05",
      "\udb81\udd06",
      "\udb81\udd07",
      "\udb81\udd08"
    ],
    "axes": [
      {
        "tag": "GRX0",
        "name": "Gradient coords, x0",
        "minimum": -1000,
        "default": 0,
        "maximum": 1000
      },
      {
        "tag": "GRY0",
        "name": "Gradient coords, y0",
        "minimum": -1000,
        "default": 0,
        "maximum": 1000
      },
      {
        "tag": "GRX1",
        "name": "Gradient coords, x1",
        "minimum": -1000,
        "default": 0,
        "maximum": 1000
      },
      {
        "tag": "GRY1",
        "name": "Gradient coords, y1",
        "minimum": -1000,
        "default": 0,
        "maximum": 1000
      },
      {
        "tag": "GRX2",
        "name": "Gradient coords, x2",
        "minimum": -1000,
        "default": 0,
        "maximum": 1000
      },
      {
        "tag": "GRY2",
        "name": "Gradient coords, y2",
        "minimum": -1000,
        "default": 0,
        "maximum": 1000
      },
      {
        "tag": "GRR0",
        "name": "Gradient coords, r0",
        "minimum": -1000,
        "default": 0,
        "maximum": 1000
      },
      {
        "tag": "GRR1",
        "name": "Gradient coords, r1",
        "minimum": -1000,
        "default": 0,
        "maximum": 1000
      },
      {
        "tag": "COL1",
        "name": "Extend tests color stop offset 1",
        "minimum": -2,
        "default": 0,
        "maximum": 2
      },
      {
        "tag": "COL2",
        "name": "Extend tests color stop offset 2",
        "minimum": -2,
        "default": 0,
        "maximum": 2
      },
      {
        "tag": "COL3",
        "name": "Extend tests color stop offset 3",
        "minimum": -2,
        "default": 0,
        "maximum": 2
      }
    ]
  },
  "paint_rotate": {
    "codepoints": [
      "\udb81\ude00",
      "\udb81\ude01",
      "\udb81\ude02",
      "\udb81\ude03"
    ],
    "axes": [
      {
        "tag": "ROTA",
        "name": "Var Rotate Angle Offset",
        "minimum": 0,
        "default": 0,
        "maximum": 539.989013671875
      },
      {
        "tag": "ROTX",
        "name": "Var Rotate Center X Offset",
        "minimum": -500,
        "default": 0,
        "maximum": 500
      },
      {
        "tag": "ROTY",
        "name": "Var Rotate Center Y Offset",
        "minimum": -500,
        "default": 0,
        "maximum": 500
      }
    ]
  },
  "paint_skew": {
    "codepoints": [
      "\udb81\udf00",
      "\udb81\udf01",
      "\udb81\udf02",
      "\udb81\udf03",
      "\udb81\udf04",
      "\udb81\udf05"
    ],
    "axes": [
      {
        "tag": "SKXA",
        "name": "Var Skew X Angle Offset",
        "minimum": -90,
        "default": 0,
        "maximum": 90
      },
      {
        "tag": "SKYA",
        "name": "Var Skew Y Angle Offset",
        "minimum": -90,
        "default": 0,
        "maximum": 90
      },
      {
        "tag": "SKCX",
        "name": "Var Skew Center X Offset",
        "minimum": -500,
        "default": 0,
        "maximum": 500
      },
      {
        "tag": "SKCY",
        "name": "Var Skew Center Y Offset",
        "minimum": -500,
        "default": 0,
        "maximum": 500
      }
    ]
  },
  "paint_transform": {
    "codepoints": [
      "\udb82\udc00",
      "\udb82\udc01",
      "\udb82\udc02",
      "\udb82\udc03"
    ],
    "axes": [
      {
        "tag": "TRXX",
        "name": "Transform scalars, xx",
        "minimum": -2,
        "default": 0,
        "maximum": 2
      },
      {
        "tag": "TRYX",
        "name": "Transform scalars, yx",
        "minimum": -2,
        "default": 0,
        "maximum": 2
      },
      {
        "tag": "TRXY",
        "name": "Transform scalars, xy",
        "minimum": -2,
        "default": 0,
        "maximum": 2
      },
      {
        "tag": "TRYY",
        "name": "Transform scalars, yy",
        "minimum": -2,
        "default": 0,
        "maximum": 2
      },
      {
        "tag": "TRDX",
        "name": "Transform scalars, dx",
        "minimum": -500,
        "default": 0,
        "maximum": 500
      },
      {
        "tag": "TRDY",
        "name": "Transform scalars, dy",
        "minimum": -500,
        "default": 0,
        "maximum": 500
      }
    ]
  },
  "paint_translate": {
    "codepoints": [
      "\udb82\udd00",
      "\udb82\udd01",
      "\udb82\udd02",
      "\udb82\udd03",
      "\udb82\udd04",
      "\udb82\udd05",
      "\udb82\udd06"
    ],
    "axes": [
      {
        "tag": "TLDX",
        "name": "Var Translate dx Offset",
        "minimum": -500,
        "default": 0,
        "maximum": 500
      },
      {
        "tag": "TLDY",
        "name": "Var Translate dy Offset",
        "minimum": -500,
        "default": 0,
        "maximum": 500
      }
    ]
  },
  "composite_mode": {
    "codepoints": [
      "\udb82\ude00",
      "\udb82\ude01",
      "\udb82\ude02",
      "\udb82\ude03",
      "\udb82\ude04",
      "\udb82\ude05",
      "\udb82\ude06",
      "\udb82\ude07",
      "\udb82\ude08",
      "\udb82\ude09",
      "\udb82\ude0a",
      "\udb82\ude0b",
      "\udb82\ude0c",
      "\udb82\ude0d",
      "\udb82\ude0e",
      "\udb82\ude0f",
      "\udb82\ude10",
      "\udb82\ude11",
      "\udb82\ude12",
      "\udb82\ude13",
      "\udb82\ude14",
      "\udb82\ude15",
      "\udb82\ude16",
      "\udb82\ude17",
      "\udb82\ude18",
      "\udb82\ude19",
      "\udb82\ude1a",
      "\udb82\ude1b"
    ],
    "axes": []
  },
  "foreground_color": {
    "codepoints": [
      "\udb82\udf00",
      "\udb82\udf01",
      "\udb82\udf02",
      "\udb82\udf03",
      "\udb82\udf04",
      "\udb82\udf05",
      "\udb82\udf06",
      "\udb82\udf07"
    ],
    "axes": []
  },
  "clipbox": {
    "codepoints": [
      "\udb83\udc00",
      "\udb83\udc01",
      "\udb83\udc02",
      "\udb83\udc03",
      "\udb83\udc04"
    ],
    "axes": [
      {
        "tag": "CLXI",
        "name": "ClipBox xMin offset",
        "minimum": -500,
        "default": 0,
        "maximum": 500
      },
      {
        "tag": "CLYI",
        "name": "ClipBox yMin offset",
        "minimum": -500,
        "default": 0,
        "maximum": 500
      },
      {
        "tag": "CLXA",
        "name": "ClipBox xMax offset",
        "minimum": -500,
        "default": 0,
        "maximum": 500
      },
      {
        "tag": "CLYA",
        "name": "ClipBox yMax offset",
        "minimum": -500,
        "default": 0,
        "maximum": 500
      },
      {
        "tag": "CLIO",
        "name": "ClipBox inner glyph inset offset",
        "minimum": -500,
        "default": 0,
        "maximum": 500
      }
    ]
  },
  "gradient_p2_skewed": {
    "codepoints": [
      "\udb83\udd00"
    ],
    "axes": []
  },
  "color_circles_palette": {
    "codepoints": [
      "\udb83\ude00",
      "\udb83\ude01"
    ],
    "axes": []
  },
  "circle_contours": {
    "codepoints": [
      "\udb83\udf00",
      "\udb83\udf01",
      "\udb83\udf02",
      "\udb83\udf03",
      "\udb83\udf04",
      "\udb83\udf05",
      "\udb83\udf06"
    ],
    "axes": []
  },
  "variable_alpha": {
    "codepoints": [
      "\udb84\udc00"
    ],
    "axes": [
      {
        "tag": "APH1",
        "name": "Alpha axis, PaintSolid",
        "minimum": -1,
        "default": 0,
        "maximum": 0
      },
      {
        "tag": "APH2",
        "name": "Alpha axis, ColorStop 0",
        "minimum": -1,
        "default": 0,
        "maximum": 0
      },
      {
        "tag": "APH3",
        "name": "Alpha axis, ColorStop 1",
        "minimum": -1,
        "default": 0,
        "maximum": 0
      }
    ]
  },
  "paintcolrglyph_cycle": {
    "codepoints": [
      "\udb84\udd00",
      "\udb84\udd01"
    ],
    "axes": []
  },
  "no_cycle_multi_colrglyph": {
    "codepoints": [
      "\udb84\ude00"
    ],
    "axes": []
  },
  "sweep_coincident": {
    "codepoints": [
      "\udb84\udf00",
      "\udb84\udf01",
      "\udb84\udf02",
      "\udb84\udf03",
      "\udb84\udf04",
      "\udb84\udf05",
      "\udb84\udf06",
      "\udb84\udf07",
      "\udb84\udf08",
      "\udb84\udf09",
      "\udb84\udf0a",
      "\udb84\udf0b",
      "\udb84\udf0c",
      "\udb84\udf0d",
      "\udb84\udf0e",
      "\udb84\udf0f",
      "\udb84\udf10",
      "\udb84\udf11",
      "\udb84\udf12",
      "\udb84\udf13",
      "\udb84\udf14",
      "\udb84\udf15",
      "\udb84\udf16",
      "\udb84\udf17"
    ],
    "axes": []
  },
  "paint_glyph_nested": {
    "codepoints": [
      "\udb85\udc00",
      "\udb85\udc01",
      "\udb85\udc02",
      "\udb85\udc03",
      "\udb85\udc04",
      "\udb85\udc05",
      "\udb85\udc06",
      "\udb85\udc07",
      "\udb85\udc08",
      "\udb85\udc09",
      "\udb85\udc0a",
      "\udb85\udc0b",
      "\udb85\udc0c",
      "\udb85\udc0d",
      "\udb85\udc0e",
      "\udb85\udc0f"
    ],
    "axes": []
  }
}
