<!DOCTYPE html>
<html>
    <head>
        <style>
            p {background:black; }
            div.reference {position: absolute; display: inline; background: blue; color: blue;}
            body {margin: 0px;}
        </style>
    </head>
    <body>
    The blue box should be right-aligned. You will need to scroll right to see it.<br>
    https://bugs.webkit.org/show_bug.cgi?id=105695
    <div id='container' style="text-align: right">
        <p>XXX</p><div id="test" class="reference">XXX</div>
    </div>
    <div id="output">Failure</div>
    <script>
        if (window.testRunner) {
          testRunner.dumpAsText();
        }
        var testEl = document.getElementById('test'); 
        if (testEl.offsetLeft == document.getElementById('container').offsetWidth)
            output.innerHTML = 'Success'; 
    </script>
    </body>
</html>
