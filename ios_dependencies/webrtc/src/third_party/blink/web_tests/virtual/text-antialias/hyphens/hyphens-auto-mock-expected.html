<!DOCTYPE html>
<style>
div {
  border: thin solid black;
  display: inline-block;
  font-family: 'Courier New';
  vertical-align: top;
  min-width: 11ch;
}

span {
  border: thin solid black;
  display: block;
}

img {
  width: 1em;
  height: 1em;
  background-color: blue;
}
</style>
<script>
if (window.internals)
  internals.setMockHyphenation('en-us');
</script>
<div lang="en-us">
  <div>
    <span style="width: 2ch;">hy-<br>phen-<br>ation<img></span>
    <span style="width: 3.1ch;">hy-<br>phen-<br>ation<img></span>
    <span style="width: 4ch;">hy-<br>phen-<br>ation<img></span>
    <span style="width: 5.1ch;">hy-<br>phen-<br>ation<img></span>
    <span style="width: 6ch;">hy-<br>phen-<br>ation<img></span>
    <span style="width: 7.1ch;">hyphen-<br>ation<img></span>
    <span style="width: 8ch;">hyphen-<br>ation<img></span>
    <span style="width: 9ch;">hyphen-<br>ation<img></span>
    <span style="width: 10ch;">hyphen-<br>ation<img></span>
    <span style="width: 11.4ch;">hyphenation<img></span>
  </div>
  <div>
    <span style="width: 2ch;">hy-<br>phen-<br>ation test</span>
    <span style="width: 3.1ch;">hy-<br>phen-<br>ation test</span>
    <span style="width: 4ch;">hy-<br>phen-<br>ation test</span>
    <span style="width: 5.1ch;">hy-<br>phen-<br>ation test</span>
    <span style="width: 6ch;">hy-<br>phen-<br>ation test</span>
    <span style="width: 7.1ch;">hyphen-<br>ation test</span>
    <span style="width: 8ch;">hyphen-<br>ation test</span>
    <span style="width: 9ch;">hyphen-<br>ation test</span>
    <span style="width: 10ch;">hyphen-<br>ation test</span>
    <span style="width: 11ch;">hyphenation test</span>
  </div>
  <div>
    <span style="width: 2ch;">a hy-<br>phen-<br>ation test</span>
    <span style="width: 3.1ch;">a hy-<br>phen-<br>ation test</span>
    <span style="width: 4ch;">a hy-<br>phen-<br>ation test</span>
    <span style="width: 5.1ch;">a hy-<br>phen-<br>ation test</span>
    <span style="width: 6ch;">a hy-<br>phen-<br>ation test</span>
    <span style="width: 7.1ch;">a hy-<br>phen-<br>ation test</span>
  </div>
  <div>
    <span style="width: 8ch;">a hy-<br>phen-<br>ation test</span>
    <span style="width: 9.1ch;">a hyphen-<br>ation test</span>
    <span style="width: 10ch;">a hyphen-<br>ation test</span>
    <span style="width: 11ch;">a hyphen-<br>ation test</span>
    <span style="width: 12ch;">a hyphen-<br>ation test</span>
    <span style="width: 13.4ch;">a hyphenation test</span>
  </div>
</div>
