<!DOCTYPE html>
<html>
<style>
@font-face {
    font-family: Ahem;
    src: url('../../../resources/Ahem.woff2') format('woff2');
}
div {
    text-align: justify;
    width: 400px;
    border: 1px solid black;
    font-family: Ahem;
}
</style>
<body>

<p>This tests the combination of text-align: justify with different values of white-space.</p>

<h4>white-space: pre-wrap</h4>
<div>Lorem &nbsp;&nbsp;ipsum dolor sit amet, consectetur adipisicing elit, &nbsp;&nbsp;&nbsp;sed do eiusmod tempor<br>
incididunt ex ea commodo consequat. &nbsp;&nbsp;&nbsp;Dolore eu fugiat nulla pariatur. Excepteur sint occaecat<br>
cupidatat non proident.
</div>

<h4>white-space: pre-line</h4>
<div>Lorem   ipsum dolor sit amet, consectetur adipisicing elit,    sed do eiusmod tempor<br>
incididunt ex ea commodo consequat.    Dolore eu fugiat nulla pariatur. Excepteur sint occaecat<br>
cupidatat non proident.
</div>

<h4>white-space: pre</h4>
<div style="white-space: pre; text-align: left;">Lorem   ipsum dolor sit amet, consectetur adipisicing elit,    sed do eiusmod tempor
incididunt ex ea commodo consequat.    Dolore eu fugiat nulla pariatur. Excepteur sint occaecat
cupidatat non proident.
</div>

<script>
    if (window.testRunner) {
        testRunner.waitUntilDone();
        window.onload = function () {
            document.fonts.ready.then(function () { testRunner.notifyDone(); });
        };
    }
</script>
</body>
</html>
