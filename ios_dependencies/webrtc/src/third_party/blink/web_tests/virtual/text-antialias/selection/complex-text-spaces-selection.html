<!DOCTYPE html>
<html>
    <head>
        <title>Spaces in Complex Text</title>
        <meta charset="utf-8">
        <style>
            div {
                font-size: 0.875em;
                width: 300px;
                font-family: Georgia;
            }
        </style>
    </head>
    <body>
        <h1>Spaces in Complex Text</h1>
        <div dir="rtl">ا . . /ا . . /ted C-130 ABCABCABC ABC ABCABC ABC ABC ABC ABC ABACBA.jpg‏؛ ‏۱۵:۳۴ . . (+۴۶۹).ABC ABCABC ABC ABC ABC ABC ABACBA.jpg‏؛ ‏۱۵:۳۴ . . (+۴۶۹).</div>
        <p>
            There should be no extra spaces or selection gaps above.
        </p>
        <script>
            var range = document.createRange();
            var el = document.getElementsByTagName('div')[0];
            range.selectNode(el);
            window.getSelection().addRange(range);
        </script>
    </body>
</html>
