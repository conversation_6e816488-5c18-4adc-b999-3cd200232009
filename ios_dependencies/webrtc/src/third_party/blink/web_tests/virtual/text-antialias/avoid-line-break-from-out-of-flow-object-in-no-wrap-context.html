<!DOCTYPE html>
<style>
.background {
    width: 80px;
    height: 80px;
    background-color: green;
}
.outer {
    white-space: nowrap;
    width: 50px;
    height: 60px;
    background-color: green;
}
.inner {
    width: 22px;
    height: 60px;
    background-color: green;
    display: inline-block;
    white-space: normal;
}
.absolute {
    position: absolute;
}
</style>
<p>crbug.com:552475: Out-of-flow elements that wrap their contents shouldn't cause a line-break in a no-wrap context. There should be a green square below.</p>
<div class="background">
    <div class="outer">
        <div class="inner"></div>
        <div class="inner"></div>
        <div class="absolute"></div>
        <div class="inner"></div>
    </div>
</div>
